// NOTICE: This file is generated by Rollup. To modify it,
// please instead edit the ESM counterpart and rebuild with Rollup (npm run build).
'use strict';

/**
 * Check whether a property is SCSS variable
 *
 * @param {string} property
 * @returns {boolean}
 */
function isScssVariable(property) {
	// SCSS var (e.g. $var: x), list (e.g. $list: (x)) or map (e.g. $map: (key:value))
	if (property.startsWith('$')) {
		return true;
	}

	// SCSS var within a namespace (e.g. namespace.$var: x)
	if (property.includes('.$')) {
		return true;
	}

	return false;
}

module.exports = isScssVariable;
