var fr = {
  LANG: "fr",
  TEXT: {
    ERROR_TYPES: {
      network: {
        code: 1,
        msg: "erreur de t\xE9l\xE9chargement vid\xE9o"
      },
      mse: {
        code: 2,
        msg: "erreur d'ajout du stream"
      },
      parse: {
        code: 3,
        msg: "erreur d'analyse"
      },
      format: {
        code: 4,
        msg: "format erron\xE9"
      },
      decoder: {
        code: 5,
        msg: "erreur de d\xE9codage"
      },
      runtime: {
        code: 6,
        msg: "erreurs grammaticales"
      },
      timeout: {
        code: 7,
        msg: "lecture expir\xE9e"
      },
      other: {
        code: 8,
        msg: "autres erreurs"
      }
    },
    HAVE_NOTHING: "Il n'y a pas d'information sur la disponibilit\xE9 de l'audio/vid\xE9o",
    HAVE_METADATA: "Les m\xE9tadonn\xE9es audio/vid\xE9o sont pr\xEAtes ",
    HAVE_CURRENT_DATA: "Les donn\xE9es relatives \xE0 l'emplacement de lecture actuel sont disponibles, mais les donn\xE9es ne sont pas suffisantes pour lire l'image/la milliseconde suivante",
    HAVE_FUTURE_DATA: "Trame de donn\xE9es actuelles (au moins une) disponible",
    HAVE_ENOUGH_DATA: "Les donn\xE9es disponibles sont suffisantes pour d\xE9marrer la lecture",
    NETWORK_EMPTY: "L'audio/vid\xE9o n'a pas \xE9t\xE9 initialis\xE9",
    NETWORK_IDLE: "L'audio/vid\xE9o est actif et a \xE9t\xE9 s\xE9lectionn\xE9 comme ressource, mais aucun r\xE9seau n'est utilis\xE9",
    NETWORK_LOADING: "Le navigateur t\xE9l\xE9charge les donn\xE9es",
    NETWORK_NO_SOURCE: "Aucune source audio/vid\xE9o n'a \xE9t\xE9 trouv\xE9e",
    MEDIA_ERR_ABORTED: "Le processus de recherche est interrompu par l'utilisateur",
    MEDIA_ERR_NETWORK: "Une erreur s'est produite lors du t\xE9l\xE9chargement",
    MEDIA_ERR_DECODE: "Une erreur s'est produite lors du d\xE9codage",
    MEDIA_ERR_SRC_NOT_SUPPORTED: "L'audio/vid\xE9o n'est pas pris en charge",
    REPLAY: "Relancer",
    ERROR: "Le r\xE9seau est hors ligne",
    PLAY_TIPS: "Lire",
    PAUSE_TIPS: "Mettre en pause",
    PLAYNEXT_TIPS: "Lire l'\xE9l\xE9ment suivant",
    DOWNLOAD_TIPS: "T\xE9l\xE9charger",
    ROTATE_TIPS: "Pivoter",
    RELOAD_TIPS: "Actualiser",
    FULLSCREEN_TIPS: "Plein \xE9cran",
    EXITFULLSCREEN_TIPS: "Quitter le plein \xE9cran",
    CSSFULLSCREEN_TIPS: "Plein \xE9cran css",
    EXITCSSFULLSCREEN_TIPS: "Quitter le plein \xE9cran css",
    TEXTTRACK: "L\xE9gende",
    PIP: "PIP",
    SCREENSHOT: "Capture d'\xE9cran",
    LIVE: "EN DIRECT",
    OFF: "D\xE9sactiv\xE9",
    OPEN: "Ouvrir",
    MINI_DRAG: "Cliquer et maintenir pour faire glisser",
    MINISCREEN: "Mini \xE9cran",
    REFRESH_TIPS: "Essayez",
    REFRESH: "Actualiser",
    FORWARD: "transf\xE9rer",
    LIVE_TIP: "En direct"
  }
};
export { fr as default };
