"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.errLog=void 0,exports.getLog=getLog,exports.warnLog=void 0;var _conf=_interopRequireDefault(require("../v-x-e-table/src/conf"));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function getLog(e,o){return"[vxe-table v4.6.25] "+_conf.default.i18n(e,o)}function outLog(r){return function(e,o){e=getLog(e,o);return console[r](e),e}}const warnLog=exports.warnLog=outLog("warn"),errLog=exports.errLog=outLog("error");