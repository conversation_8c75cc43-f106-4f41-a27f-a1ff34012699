@use './helpers/baseMixin.scss';
@use './base/common.scss';

/*radio-group*/
.vxe-radio-group {
  display: inline-block;
  vertical-align: middle;
  line-height: 1;
  font-size: 0;
  &+.vxe-radio-group {
    margin-left: 10px;
  }
}

/*radio*/
.vxe-radio {
  display: inline-block;
  vertical-align: middle;
  white-space: nowrap;
  line-height: 1;
  @include baseMixin.createRadioIcon();
  > input {
    &[type="radio"] {
      position: absolute;
      width: 0;
      height: 0;
      border: 0;
      appearance: none;
    }
  }
  .vxe-radio--label {
    vertical-align: middle;
    display: inline-block;
    max-width: 50em;
    @extend %TextEllipsis;
  }
  &:not(.is--disabled) {
    > input {
      &:focus+.vxe-radio--icon {
        color: var(--vxe-primary-color);
      }
    }
  }
  &:not(.vxe-radio-button) {
    &+.vxe-radio {
      margin-left: 10px;
    }
  }
}

.vxe-radio-button {
  .vxe-radio--label {
    background-color: var(--vxe-radio-button-default-background-color);
  }
  &:first-child {
    .vxe-radio--label {
      border-left: 1px solid var(--vxe-input-border-color);
      border-radius: var(--vxe-border-radius) 0 0 var(--vxe-border-radius);
    }
  }
  &:last-child {
    .vxe-radio--label {
      border-radius: 0 var(--vxe-border-radius) var(--vxe-border-radius) 0;
    }
  }
  > input {
    &:checked+.vxe-radio--label {
      color: #fff;
      background-color: var(--vxe-primary-color);
      border-color: var(--vxe-primary-color);
    }
  }
  .vxe-radio--label {
    padding: 0 1em;
    line-height: calc(var(--vxe-button-height-default) - 2px);
    display: inline-block;
    border-style: solid;
    border-color: var(--vxe-input-border-color);
    border-width: 1px 1px 1px 0;
    max-width: 50em;
    @extend %TextEllipsis;
  }
  &.is--disabled {
    cursor: not-allowed;
    > input {
      &:not(:checked) {
        &+.vxe-radio--label {
          color: var(--vxe-input-disabled-color);
        }
      }
      &:checked {
        &+.vxe-radio--label {
          border-color: var(--vxe-primary-lighten-color);
          background-color: var(--vxe-primary-lighten-color);
        }
      }
    }
  }
  &:not(.is--disabled) {
    & > input {
      &:focus {
        &+.vxe-radio--label {
          border-color: var(--vxe-primary-color);
          box-shadow: 0 0 0.2em 0 var(--vxe-primary-color);
        }
      }
    }
    &:hover {
      > input {
        &:not(:checked) {
          &+.vxe-radio--label {
            color: var(--vxe-primary-color);
          }
        }
      }
    }
  }
  &.size--medium {
    .vxe-radio--label {
      line-height: calc(var(--vxe-button-height-medium) - 2px);
    }
  }
  &.size--small {
    .vxe-radio--label {
      line-height: calc(var(--vxe-button-height-small) - 2px);
    }
  }
  &.size--mini {
    .vxe-radio--label {
      line-height: calc(var(--vxe-button-height-mini) - 2px);
    }
  }
}

.vxe-radio {
  font-size: var(--vxe-font-size);
  &.size--medium {
    font-size: var(--vxe-font-size-medium);
  }
  &.size--small {
    font-size: var(--vxe-font-size-small);
  }
  &.size--mini {
    font-size: var(--vxe-font-size-mini);
  }
}