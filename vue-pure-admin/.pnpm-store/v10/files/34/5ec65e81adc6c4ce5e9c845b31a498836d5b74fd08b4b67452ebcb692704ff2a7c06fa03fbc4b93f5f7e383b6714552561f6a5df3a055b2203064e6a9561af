declare const _sfc_main: import("vue").DefineComponent<{
    type: {
        type: StringConstructor;
        default: string;
    };
    canvasWidth: {
        type: NumberConstructor;
        default: number;
    };
    canvasHeight: {
        type: NumberConstructor;
        default: number;
    };
    show: {
        type: BooleanConstructor;
        default: boolean;
    };
    puzzleScale: {
        type: NumberConstructor;
        default: number;
    };
    sliderSize: {
        type: NumberConstructor;
        default: number;
    };
    range: {
        type: NumberConstructor;
        default: number;
    };
    zIndex: {
        type: NumberConstructor;
        default: number;
    };
    imgs: {
        type: ArrayConstructor;
        default: null;
    };
    successText: {
        type: StringConstructor;
        default: string;
    };
    failText: {
        type: StringConstructor;
        default: string;
    };
    sliderText: {
        type: StringConstructor;
        default: string;
    };
    className: {
        type: StringConstructor;
        default: string;
    };
}, {
    reset: (needEmit?: boolean) => void;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("success" | "fail" | "close" | "reset")[], "success" | "fail" | "close" | "reset", import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    type: {
        type: StringConstructor;
        default: string;
    };
    canvasWidth: {
        type: NumberConstructor;
        default: number;
    };
    canvasHeight: {
        type: NumberConstructor;
        default: number;
    };
    show: {
        type: BooleanConstructor;
        default: boolean;
    };
    puzzleScale: {
        type: NumberConstructor;
        default: number;
    };
    sliderSize: {
        type: NumberConstructor;
        default: number;
    };
    range: {
        type: NumberConstructor;
        default: number;
    };
    zIndex: {
        type: NumberConstructor;
        default: number;
    };
    imgs: {
        type: ArrayConstructor;
        default: null;
    };
    successText: {
        type: StringConstructor;
        default: string;
    };
    failText: {
        type: StringConstructor;
        default: string;
    };
    sliderText: {
        type: StringConstructor;
        default: string;
    };
    className: {
        type: StringConstructor;
        default: string;
    };
}>> & {
    onSuccess?: ((...args: any[]) => any) | undefined;
    onFail?: ((...args: any[]) => any) | undefined;
    onClose?: ((...args: any[]) => any) | undefined;
    onReset?: ((...args: any[]) => any) | undefined;
}, {
    type: string;
    canvasWidth: number;
    canvasHeight: number;
    show: boolean;
    puzzleScale: number;
    sliderSize: number;
    range: number;
    zIndex: number;
    imgs: unknown[];
    successText: string;
    failText: string;
    sliderText: string;
    className: string;
}>;
export default _sfc_main;
