"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.addClass=addClass,exports.browse=void 0,exports.getAbsolutePos=getAbsolutePos,exports.getDomNode=getDomNode,exports.getEventTargetNode=getEventTargetNode,exports.getOffsetHeight=getOffsetHeight,exports.getOffsetPos=getOffsetPos,exports.getPaddingTopBottomSize=getPaddingTopBottomSize,exports.getPropClass=getPropClass,exports.hasClass=hasClass,exports.isNodeElement=isNodeElement,exports.isPx=isPx,exports.isScale=isScale,exports.removeClass=removeClass,exports.scrollToView=scrollToView,exports.setScrollLeft=setScrollLeft,exports.setScrollTop=setScrollTop,exports.triggerEvent=triggerEvent,exports.updateCellTitle=updateCellTitle;var _xeUtils=_interopRequireDefault(require("xe-utils"));function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}const reClsMap={},browse=exports.browse=_xeUtils.default.browse();function getPropClass(e,t){return e?_xeUtils.default.isFunction(e)?e(t):e:""}function getClsRE(e){return reClsMap[e]||(reClsMap[e]=new RegExp(`(?:^|\\s)${e}(?!\\S)`,"g")),reClsMap[e]}function getNodeOffset(e,t,o){if(e){var s=e.parentNode;if(o.top+=e.offsetTop,o.left+=e.offsetLeft,s&&s!==document.documentElement&&s!==document.body&&(o.top-=s.scrollTop,o.left-=s.scrollLeft),(!t||e!==t&&e.offsetParent!==t)&&e.offsetParent)return getNodeOffset(e.offsetParent,t,o)}return o}function isPx(e){return e&&/^\d+(px)?$/.test(e)}function isScale(e){return e&&/^\d+%$/.test(e)}function hasClass(e,t){return e&&e.className&&e.className.match&&e.className.match(getClsRE(t))}function removeClass(e,t){e&&hasClass(e,t)&&(e.className=e.className.replace(getClsRE(t),""))}function addClass(e,t){e&&!hasClass(e,t)&&(removeClass(e,t),e.className=e.className+" "+t)}function getDomNode(){var e=document.documentElement,t=document.body;return{scrollTop:e.scrollTop||t.scrollTop,scrollLeft:e.scrollLeft||t.scrollLeft,visibleHeight:e.clientHeight||t.clientHeight,visibleWidth:e.clientWidth||t.clientWidth}}function getOffsetHeight(e){return e?e.offsetHeight:0}function getPaddingTopBottomSize(e){return e?(e=getComputedStyle(e),_xeUtils.default.toNumber(e.paddingTop)+_xeUtils.default.toNumber(e.paddingBottom)):0}function setScrollTop(e,t){e&&(e.scrollTop=t)}function setScrollLeft(e,t){e&&(e.scrollLeft=t)}function updateCellTitle(e,t){t="html"===t.type?e.innerText:e.textContent;e.getAttribute("title")!==t&&e.setAttribute("title",t)}function getEventTargetNode(e,t,o,s){let l,r=e.target.shadowRoot&&e.composed&&e.composedPath()[0]||e.target;for(;r&&r.nodeType&&r!==document;){if(o&&hasClass(r,o)&&(!s||s(r)))l=r;else if(r===t)return{flag:!o||!!l,container:t,targetElem:l};r=r.parentNode}return{flag:!1}}function getOffsetPos(e,t){return getNodeOffset(e,t,{left:0,top:0})}function getAbsolutePos(e){var e=e.getBoundingClientRect(),t=e.top,e=e.left,{scrollTop:o,scrollLeft:s,visibleHeight:l,visibleWidth:r}=getDomNode();return{boundingTop:t,top:o+t,boundingLeft:e,left:s+e,visibleHeight:l,visibleWidth:r}}const scrollIntoViewIfNeeded="scrollIntoViewIfNeeded",scrollIntoView="scrollIntoView";function scrollToView(e){e&&(e[scrollIntoViewIfNeeded]?e[scrollIntoViewIfNeeded]():e[scrollIntoView]&&e[scrollIntoView]())}function triggerEvent(e,t){e&&e.dispatchEvent(new Event(t))}function isNodeElement(e){return e&&1===e.nodeType}