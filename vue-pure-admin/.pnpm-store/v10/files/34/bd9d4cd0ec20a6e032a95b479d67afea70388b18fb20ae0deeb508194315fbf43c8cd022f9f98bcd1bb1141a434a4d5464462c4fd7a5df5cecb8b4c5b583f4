{"name": "tinygradient", "version": "1.1.5", "author": {"name": "<PERSON> \"<PERSON><PERSON>\" <PERSON>", "email": "<EMAIL>", "url": "https://www.strangeplanet.fr"}, "description": "Fast and small gradients manipulation, built on top of TinyColor", "license": "MIT", "homepage": "https://github.com/mistic100/tinygradient", "main": "index.js", "browser": "browser.js", "types": "types.d.ts", "files": ["browser.js", "browser.js.map", "index.js", "types.d.ts"], "dependencies": {"@types/tinycolor2": "^1.4.0", "tinycolor2": "^1.0.0"}, "devDependencies": {"@babel/core": "^7.2.2", "@babel/preset-env": "^7.2.3", "colorbrewer": "^1.3.0", "live-server": "^1.2.1", "mocha": "^8.0.1", "nodemon": "^2.0.0", "npm-run-all": "^4.1.5", "rollup": "^2.0.2", "rollup-plugin-babel": "^4.3.0", "rollup-plugin-commonjs": "^10.0.0"}, "keywords": ["color", "gradient"], "repository": {"type": "git", "url": "git://github.com/mistic100/tinygradient.git"}, "bugs": {"url": "https://github.com/mistic100/tinygradient/issues"}, "scripts": {"test": "mocha tests/*", "compile": "rollup --config rollup.config.js", "serve": "live-server --watch=browser.js,index.html --entry-file=index.html", "start": "npm-run-all --parallel watch serve", "watch": "nodemon --watch index.js --exec \"npm run compile\""}}