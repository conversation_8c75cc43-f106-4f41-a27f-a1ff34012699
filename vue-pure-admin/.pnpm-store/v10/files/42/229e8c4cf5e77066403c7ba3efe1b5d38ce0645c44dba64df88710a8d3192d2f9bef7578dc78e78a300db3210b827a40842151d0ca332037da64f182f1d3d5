import * as esbuild from 'esbuild';
import { BuildOptions, Plugin, Loader, TransformOptions } from 'esbuild';
export * from 'esbuild';
export { BuildOptions, ImportKind, Loader, Metafile, Plugin } from 'esbuild';

var version = "0.0.5";

declare function buildBundler(esbuildOptions?: BuildOptions): Promise<esbuild.BuildResult<{
    bundle: boolean;
    splitting?: boolean;
    preserveSymlinks?: boolean;
    outfile?: string;
    metafile: boolean;
    outdir?: string;
    outbase?: string;
    external?: string[];
    packages?: "bundle" | "external";
    alias?: Record<string, string>;
    loader?: {
        [ext: string]: esbuild.Loader;
    };
    resolveExtensions?: string[];
    mainFields?: string[];
    conditions?: string[];
    write: boolean;
    allowOverwrite?: boolean;
    tsconfig?: string;
    outExtension?: {
        [ext: string]: string;
    };
    publicPath?: string;
    entryNames?: string;
    chunkNames?: string;
    assetNames?: string;
    inject?: string[];
    banner?: {
        [type: string]: string;
    };
    footer?: {
        [type: string]: string;
    };
    entryPoints?: string[] | Record<string, string> | {
        in: string;
        out: string;
    }[];
    stdin?: esbuild.StdinOptions;
    plugins: esbuild.Plugin[];
    absWorkingDir?: string;
    nodePaths?: string[];
    sourcemap?: boolean | "linked" | "inline" | "external" | "both";
    legalComments: "none" | "inline" | "eof" | "linked" | "external";
    sourceRoot?: string;
    sourcesContent?: boolean;
    format: esbuild.Format;
    globalName?: string;
    target?: string | string[];
    supported?: Record<string, boolean>;
    platform: esbuild.Platform;
    mangleProps?: RegExp;
    reserveProps?: RegExp;
    mangleQuoted?: boolean;
    mangleCache?: Record<string, string | false>;
    drop?: esbuild.Drop[];
    dropLabels?: string[];
    minify?: boolean;
    minifyWhitespace?: boolean;
    minifyIdentifiers?: boolean;
    minifySyntax?: boolean;
    lineLimit?: number;
    charset?: esbuild.Charset;
    treeShaking?: boolean;
    ignoreAnnotations?: boolean;
    jsx?: "transform" | "preserve" | "automatic";
    jsxFactory?: string;
    jsxFragment?: string;
    jsxImportSource?: string;
    jsxDev?: boolean;
    jsxSideEffects?: boolean;
    define?: {
        [key: string]: string;
    };
    pure?: string[];
    keepNames?: boolean;
    color?: boolean;
    logLevel: esbuild.LogLevel;
    logLimit?: number;
    logOverride?: Record<string, esbuild.LogLevel>;
    tsconfigRaw?: string | esbuild.TsconfigRaw;
}>>;

declare function externalPlugin(): Plugin;

declare const IMPORT_META_URL_VAR_NAME = "__injected_import_meta_url__";
declare const JS_EXT_RE: RegExp;
declare function inferLoader(ext: string): Loader;
declare function injectFileScopePlugin(): Plugin;

/**
 * Options for importing code from a string.
 */
interface ImportFromStringOptions {
    /**
     * The virtual file name of the code to import.
     * @default `${Date.now()}.js`
     */
    filename?: string;
    /**
     * The directory name to resolve dependencies relative to.
     * @default The directory where the function is called
     */
    dirname?: string;
    /**
     * esbuild transform options.
     */
    transformOptions?: TransformOptions;
    /**
     * esbuild options.
     */
    esbuildOptions?: BuildOptions;
    /**
     * skip esbuild build.
     * @default false
     */
    skipBuild?: boolean;
}
/**
 * Asynchronously import module from string.
 *
 * @param code - The code to import.
 * @param options - Options for importing the code.
 * @returns A promise that resolves to the imported module.
 * @throws If the import or transformation process encounters an error.
 */
declare function importFromString(code: string, options?: ImportFromStringOptions): Promise<any>;

/**
 * Options for requiring code from a string.
 */
interface RequireFromStringOptions {
    /**
     * The virtual file name of the code to import.
     * @default `${Date.now()}.js`
     */
    filename?: string;
    /**
     * The directory name to resolve dependencies relative to.
     * @default The directory where the function is called
     */
    dirname?: string;
    /**
     * An array of additional paths to append when resolving modules.
     */
    appendPaths?: string[];
    /**
     * An array of additional paths to prepend when resolving modules.
     */
    prependPaths?: string[];
}
/**
 * Requires code from a string.
 *
 * @param code - The code to require.
 * @param options - Options for requiring the code.
 * @returns The exports object of the required module.
 * @throws If the code is empty or encounters an error during execution.
 */
declare function requireFromString(code: string, options?: RequireFromStringOptions): any;

/**
 * Checks if a given URL is a file URL.
 *
 * @param value - The URL to check.
 * @returns `true` if the URL is a file URL, `false` otherwise.
 */
declare const isFileURL: (value: string) => boolean;
/**
 * Ensures that a given value is a file URL. If the value is not a file URL,
 * it converts it to a file URL using `pathToFileURL`.
 *
 * @param value - The value to ensure as a file URL.
 * @returns The file URL.
 */
declare const ensureFileURL: (value: string) => string;
/**
 * Ensures that a given value is a file path. If the value is a file URL,
 * it converts it to a file path using `fileURLToPath`.
 *
 * @param value - The value to ensure as a file path.
 * @returns The file path.
 */
declare const ensurePath: (value: string) => string;
/**
 * Checks if the current execution context is in an ECMAScript module scope.
 *
 * @returns `true` if the current context is in an ES module scope, `false` otherwise.
 */
declare function isInESModuleScope(): boolean;
/**
 * Retrieves the directory name of the caller function.
 *
 * @returns The directory name of the caller.
 */
declare function getCallerDirname(): string;
/**
 * Retrieves an array of node_modules paths starting from the given file path and going up the directory tree.
 *
 * @param filePath - The file path to start from.
 * @returns An array of node_modules paths.
 */
declare function getNodeModulesPaths(filePath: string): string[];

export { IMPORT_META_URL_VAR_NAME, type ImportFromStringOptions, JS_EXT_RE, type RequireFromStringOptions, buildBundler, ensureFileURL, ensurePath, externalPlugin, getCallerDirname, getNodeModulesPaths, importFromString, inferLoader, injectFileScopePlugin, isFileURL, isInESModuleScope, requireFromString, version };
