var pt = {
  name: "pt",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "Confirmar",
      clear: "<PERSON><PERSON>"
    },
    datepicker: {
      now: "<PERSON>gora",
      today: "<PERSON><PERSON>",
      cancel: "Cancelar",
      clear: "<PERSON><PERSON>",
      confirm: "Confirmar",
      selectDate: "Selecione a data",
      selectTime: "Selecione a hora",
      startDate: "Data de inicio",
      startTime: "Hora de inicio",
      endDate: "Data de fim",
      endTime: "Hora de fim",
      prevYear: "Previous Year",
      nextYear: "Next Year",
      prevMonth: "Previous Month",
      nextMonth: "Next Month",
      year: "",
      month1: "Janeiro",
      month2: "Fevereiro",
      month3: "Mar\xE7o",
      month4: "Abril",
      month5: "Maio",
      month6: "Junho",
      month7: "Julho",
      month8: "Agosto",
      month9: "Setembro",
      month10: "Outubro",
      month11: "Novembro",
      month12: "Dezemb<PERSON>",
      weeks: {
        sun: "Dom",
        mon: "Seg",
        tue: "Ter",
        wed: "Qua",
        thu: "Qui",
        fri: "Sex",
        sat: "Sab"
      },
      months: {
        jan: "Jan",
        feb: "Fev",
        mar: "Mar",
        apr: "Abr",
        may: "Mai",
        jun: "Jun",
        jul: "Jul",
        aug: "Ago",
        sep: "Set",
        oct: "Out",
        nov: "Nov",
        dec: "Dez"
      }
    },
    select: {
      loading: "A carregar",
      noMatch: "Sem correspond\xEAncia",
      noData: "Sem dados",
      placeholder: "Selecione"
    },
    mention: {
      loading: "A carregar"
    },
    cascader: {
      noMatch: "Sem correspond\xEAncia",
      loading: "A carregar",
      placeholder: "Selecione",
      noData: "Sem dados"
    },
    pagination: {
      goto: "Ir para",
      pagesize: "/pagina",
      total: "Total {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Mensagem",
      confirm: "Confirmar",
      cancel: "Cancelar",
      error: "Erro!"
    },
    upload: {
      deleteTip: "press delete to remove",
      delete: "Apagar",
      preview: "Previsualizar",
      continue: "Continuar"
    },
    table: {
      emptyText: "Sem dados",
      confirmFilter: "Confirmar",
      resetFilter: "Limpar",
      clearFilter: "Todos",
      sumText: "Sum"
    },
    tree: {
      emptyText: "Sem dados"
    },
    transfer: {
      noMatch: "Sem correspond\xEAncia",
      noData: "Sem dados",
      titles: ["List 1", "List 2"],
      filterPlaceholder: "Enter keyword",
      noCheckedFormat: "{total} items",
      hasCheckedFormat: "{checked}/{total} checked"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

export { pt as default };
//# sourceMappingURL=pt.mjs.map
