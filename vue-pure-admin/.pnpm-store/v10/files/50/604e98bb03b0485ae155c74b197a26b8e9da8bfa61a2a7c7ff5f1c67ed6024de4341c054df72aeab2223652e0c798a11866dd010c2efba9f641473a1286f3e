/*!
  Theme: <PERSON><PERSON>esh
  Author: <PERSON> (http://github.com/Alexx2/)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#948e48;background:#201602}.hljs ::selection,.hljs::selection{background-color:#5f5b17;color:#948e48}.hljs-comment{color:#6c6823}.hljs-tag{color:#86813b}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#948e48}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#c35359}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#b36144}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#a88339}.hljs-strong{font-weight:700;color:#a88339}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#18974e}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#75a738}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#477ca1}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#8868b3}.hljs-emphasis{color:#8868b3;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#b3588e}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}
