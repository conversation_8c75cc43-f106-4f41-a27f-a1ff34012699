import { PlusRadioProps, PlusDatePickerProps, PlusInputTagProps } from 'plus-pro-components';
import { TimeSelectProps } from 'element-plus/es/components/time-select/src/time-select';
import { LooseRequired } from '@vue/shared';
import { FormItemRule, RowProps, ColProps, CardProps, FormItemProp, FormItemContext, FormValidateCallback, FormValidationResult, ElTooltipProps, TableColumnCtx, FormItemProps, ProgressFn, CascaderNode, InputAutoSize, AutocompleteProps, CheckboxGroupProps, ColorPickerProps, DatePickerProps, InputProps, InputNumberProps, RadioGroupProps, RateProps, ISelectProps, SliderProps, SwitchProps, TimePickerDefaultProps, TextProps, ImageProps, LinkProps, TagProps, ProgressProps } from 'element-plus';
import { Arrayable, EpPropFinalized, EpPropMergeType } from 'element-plus/es/utils';
import { PlusFormGroupRow, PlusFormProps } from './src/type';
import { PlusColumn, FieldValues, Mutable, TableValueType, FormItemValueType, RecordType, OptionsType, OptionsRow, RenderTypes, FieldValueType, PropsItemType, OmitTypes, CascaderProps } from 'plus-pro-components';
import { CreateComponentPublicInstance, ExtractPropTypes, PropType, Ref, ComputedRef, ComponentOptionsMixin, VNodeProps, AllowedComponentProps, ComponentCustomProps, ComponentOptionsBase, CSSProperties, Component } from 'vue';
import { default as Form } from './src/index.vue';

export type PlusFormInstance = InstanceType<typeof Form>;
export * from './src/type';
export declare const PlusForm: {
    new (...args: any[]): CreateComponentPublicInstance<Readonly< ExtractPropTypes<{
        columns: {
            type: PropType< PlusColumn[]>;
            default: () => never[];
        };
        group: {
            type: PropType<false | PlusFormGroupRow[]>;
            default: boolean;
        };
        modelValue: {
            type: PropType<FieldValues>;
            default: () => {};
        };
        rules: {
            type: PropType<Partial<Record<string, Arrayable<FormItemRule>>>>;
            default: () => {};
        };
        labelPosition: {
            type: PropType<"top" | "right" | "left">;
            default: string;
        };
        labelWidth: {
            type: PropType<string | number>;
            default: string;
        };
        labelSuffix: {
            type: PropType<string>;
            default: string;
        };
        hasLabel: {
            type: PropType<boolean>;
            default: boolean;
        };
        defaultValues: {
            type: PropType<FieldValues>;
            default: () => {};
        };
        rowProps: {
            type: PropType<Partial< Mutable<RowProps>>>;
            default: () => {};
        };
        colProps: {
            type: PropType<Partial< Mutable<ColProps>>>;
            default: () => {};
        };
        hasErrorTip: {
            type: PropType<boolean>;
            default: boolean;
        };
        hasFooter: {
            type: PropType<boolean>;
            default: boolean;
        };
        hasReset: {
            type: PropType<boolean>;
            default: boolean;
        };
        submitText: {
            type: PropType<string>;
            default: string;
        };
        resetText: {
            type: PropType<string>;
            default: string;
        };
        submitLoading: {
            type: PropType<boolean>;
            default: boolean;
        };
        footerAlign: {
            type: PropType<"right" | "left" | "center">;
            default: string;
        };
        cardProps: {
            type: PropType<Partial< Mutable<CardProps>>>;
            default: () => {};
        };
        prevent: {
            type: PropType<boolean>;
            default: boolean;
        };
        collapseDuration: {
            type: PropType<number>;
            default: undefined;
        };
        collapseTransition: {
            type: PropType<boolean>;
            default: undefined;
        };
        clearable: {
            type: PropType<boolean>;
            default: boolean;
        };
    }>> & {
        onChange?: ((values: FieldValues, column: PlusColumn) => any) | undefined;
        "onUpdate:modelValue"?: ((values: FieldValues) => any) | undefined;
        onReset?: ((values: FieldValues) => any) | undefined;
        onSubmit?: ((values: FieldValues) => any) | undefined;
        onValidate?: ((...args: any[]) => any) | undefined;
        onSubmitError?: ((errors: unknown) => any) | undefined;
    }, {
        formInstance: Ref< CreateComponentPublicInstance<Readonly< ExtractPropTypes<{
            readonly model: ObjectConstructor;
            readonly rules: {
                readonly type: PropType<Partial<Record<string, Arrayable<FormItemRule>>>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly labelPosition: EpPropFinalized<StringConstructor, "top" | "right" | "left", unknown, "right", boolean>;
            readonly requireAsteriskPosition: EpPropFinalized<StringConstructor, "right" | "left", unknown, "left", boolean>;
            readonly labelWidth: EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
            readonly labelSuffix: EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
            readonly inline: BooleanConstructor;
            readonly inlineMessage: BooleanConstructor;
            readonly statusIcon: BooleanConstructor;
            readonly showMessage: EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly validateOnRuleChange: EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly hideRequiredAsterisk: BooleanConstructor;
            readonly scrollToError: BooleanConstructor;
            readonly scrollIntoViewOptions: {
                readonly type: PropType<EpPropMergeType<readonly [ObjectConstructor, BooleanConstructor], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly size: {
                readonly type: PropType<EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly disabled: BooleanConstructor;
        }>> & {
            onValidate?: ((prop: FormItemProp, isValid: boolean, message: string) => any) | undefined;
        }, {
            COMPONENT_NAME: string;
            props: Readonly< LooseRequired<Readonly< ExtractPropTypes<{
                readonly model: ObjectConstructor;
                readonly rules: {
                    readonly type: PropType<Partial<Record<string, Arrayable<FormItemRule>>>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly labelPosition: EpPropFinalized<StringConstructor, "top" | "right" | "left", unknown, "right", boolean>;
                readonly requireAsteriskPosition: EpPropFinalized<StringConstructor, "right" | "left", unknown, "left", boolean>;
                readonly labelWidth: EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
                readonly labelSuffix: EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
                readonly inline: BooleanConstructor;
                readonly inlineMessage: BooleanConstructor;
                readonly statusIcon: BooleanConstructor;
                readonly showMessage: EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
                readonly validateOnRuleChange: EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
                readonly hideRequiredAsterisk: BooleanConstructor;
                readonly scrollToError: BooleanConstructor;
                readonly scrollIntoViewOptions: {
                    readonly type: PropType<EpPropMergeType<readonly [ObjectConstructor, BooleanConstructor], unknown, unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly size: {
                    readonly type: PropType<EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly disabled: BooleanConstructor;
            }>> & {
                onValidate?: ((prop: FormItemProp, isValid: boolean, message: string) => any) | undefined;
            }>>;
            emit: (event: "validate", prop: FormItemProp, isValid: boolean, message: string) => void;
            fields: FormItemContext[];
            formSize: ComputedRef<"" | "default" | "small" | "large">;
            ns: {
                namespace: ComputedRef<string>;
                b: (blockSuffix?: string | undefined) => string;
                e: (element?: string | undefined) => string;
                m: (modifier?: string | undefined) => string;
                be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                em: (element?: string | undefined, modifier?: string | undefined) => string;
                bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                is: {
                    (name: string, state: boolean | undefined): string;
                    (name: string): string;
                };
                cssVar: (object: Record<string, string>) => Record<string, string>;
                cssVarName: (name: string) => string;
                cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                cssVarBlockName: (name: string) => string;
            };
            formClasses: ComputedRef<(string | {
                [x: string]: boolean | EpPropMergeType<StringConstructor, "top" | "right" | "left", unknown>;
            })[]>;
            getField: (prop: string) => FormItemContext | undefined;
            addField: (field: FormItemContext) => void;
            removeField: (field: FormItemContext) => void;
            resetFields: (props?: Arrayable<FormItemProp> | undefined) => void;
            clearValidate: (props?: Arrayable<FormItemProp> | undefined) => void;
            isValidatable: ComputedRef<boolean>;
            obtainValidateFields: (props: Arrayable<FormItemProp>) => FormItemContext[];
            validate: (callback?: FormValidateCallback | undefined) => FormValidationResult;
            doValidateField: (props?: Arrayable<FormItemProp> | undefined) => Promise<boolean>;
            validateField: (props?: Arrayable<FormItemProp> | undefined, callback?: FormValidateCallback | undefined) => FormValidationResult;
            scrollToField: (prop: FormItemProp) => void;
        }, unknown, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
            validate: (prop: FormItemProp, isValid: boolean, message: string) => boolean;
        }, VNodeProps & AllowedComponentProps & ComponentCustomProps & Readonly< ExtractPropTypes<{
            readonly model: ObjectConstructor;
            readonly rules: {
                readonly type: PropType<Partial<Record<string, Arrayable<FormItemRule>>>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly labelPosition: EpPropFinalized<StringConstructor, "top" | "right" | "left", unknown, "right", boolean>;
            readonly requireAsteriskPosition: EpPropFinalized<StringConstructor, "right" | "left", unknown, "left", boolean>;
            readonly labelWidth: EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
            readonly labelSuffix: EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
            readonly inline: BooleanConstructor;
            readonly inlineMessage: BooleanConstructor;
            readonly statusIcon: BooleanConstructor;
            readonly showMessage: EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly validateOnRuleChange: EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly hideRequiredAsterisk: BooleanConstructor;
            readonly scrollToError: BooleanConstructor;
            readonly scrollIntoViewOptions: {
                readonly type: PropType<EpPropMergeType<readonly [ObjectConstructor, BooleanConstructor], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly size: {
                readonly type: PropType<EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly disabled: BooleanConstructor;
        }>> & {
            onValidate?: ((prop: FormItemProp, isValid: boolean, message: string) => any) | undefined;
        }, {
            readonly disabled: boolean;
            readonly labelPosition: EpPropMergeType<StringConstructor, "top" | "right" | "left", unknown>;
            readonly requireAsteriskPosition: EpPropMergeType<StringConstructor, "right" | "left", unknown>;
            readonly labelWidth: EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown>;
            readonly labelSuffix: string;
            readonly showMessage: EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly validateOnRuleChange: EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly inline: boolean;
            readonly inlineMessage: boolean;
            readonly statusIcon: boolean;
            readonly hideRequiredAsterisk: boolean;
            readonly scrollToError: boolean;
        }, true, {}, {}, {
            P: {};
            B: {};
            D: {};
            C: {};
            M: {};
            Defaults: {};
        }, Readonly< ExtractPropTypes<{
            readonly model: ObjectConstructor;
            readonly rules: {
                readonly type: PropType<Partial<Record<string, Arrayable<FormItemRule>>>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly labelPosition: EpPropFinalized<StringConstructor, "top" | "right" | "left", unknown, "right", boolean>;
            readonly requireAsteriskPosition: EpPropFinalized<StringConstructor, "right" | "left", unknown, "left", boolean>;
            readonly labelWidth: EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
            readonly labelSuffix: EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
            readonly inline: BooleanConstructor;
            readonly inlineMessage: BooleanConstructor;
            readonly statusIcon: BooleanConstructor;
            readonly showMessage: EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly validateOnRuleChange: EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly hideRequiredAsterisk: BooleanConstructor;
            readonly scrollToError: BooleanConstructor;
            readonly scrollIntoViewOptions: {
                readonly type: PropType<EpPropMergeType<readonly [ObjectConstructor, BooleanConstructor], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly size: {
                readonly type: PropType<EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly disabled: BooleanConstructor;
        }>> & {
            onValidate?: ((prop: FormItemProp, isValid: boolean, message: string) => any) | undefined;
        }, {
            COMPONENT_NAME: string;
            props: Readonly< LooseRequired<Readonly< ExtractPropTypes<{
                readonly model: ObjectConstructor;
                readonly rules: {
                    readonly type: PropType<Partial<Record<string, Arrayable<FormItemRule>>>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly labelPosition: EpPropFinalized<StringConstructor, "top" | "right" | "left", unknown, "right", boolean>;
                readonly requireAsteriskPosition: EpPropFinalized<StringConstructor, "right" | "left", unknown, "left", boolean>;
                readonly labelWidth: EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
                readonly labelSuffix: EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
                readonly inline: BooleanConstructor;
                readonly inlineMessage: BooleanConstructor;
                readonly statusIcon: BooleanConstructor;
                readonly showMessage: EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
                readonly validateOnRuleChange: EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
                readonly hideRequiredAsterisk: BooleanConstructor;
                readonly scrollToError: BooleanConstructor;
                readonly scrollIntoViewOptions: {
                    readonly type: PropType<EpPropMergeType<readonly [ObjectConstructor, BooleanConstructor], unknown, unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly size: {
                    readonly type: PropType<EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly disabled: BooleanConstructor;
            }>> & {
                onValidate?: ((prop: FormItemProp, isValid: boolean, message: string) => any) | undefined;
            }>>;
            emit: (event: "validate", prop: FormItemProp, isValid: boolean, message: string) => void;
            fields: FormItemContext[];
            formSize: ComputedRef<"" | "default" | "small" | "large">;
            ns: {
                namespace: ComputedRef<string>;
                b: (blockSuffix?: string | undefined) => string;
                e: (element?: string | undefined) => string;
                m: (modifier?: string | undefined) => string;
                be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                em: (element?: string | undefined, modifier?: string | undefined) => string;
                bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                is: {
                    (name: string, state: boolean | undefined): string;
                    (name: string): string;
                };
                cssVar: (object: Record<string, string>) => Record<string, string>;
                cssVarName: (name: string) => string;
                cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                cssVarBlockName: (name: string) => string;
            };
            formClasses: ComputedRef<(string | {
                [x: string]: boolean | EpPropMergeType<StringConstructor, "top" | "right" | "left", unknown>;
            })[]>;
            getField: (prop: string) => FormItemContext | undefined;
            addField: (field: FormItemContext) => void;
            removeField: (field: FormItemContext) => void;
            resetFields: (props?: Arrayable<FormItemProp> | undefined) => void;
            clearValidate: (props?: Arrayable<FormItemProp> | undefined) => void;
            isValidatable: ComputedRef<boolean>;
            obtainValidateFields: (props: Arrayable<FormItemProp>) => FormItemContext[];
            validate: (callback?: FormValidateCallback | undefined) => FormValidationResult;
            doValidateField: (props?: Arrayable<FormItemProp> | undefined) => Promise<boolean>;
            validateField: (props?: Arrayable<FormItemProp> | undefined, callback?: FormValidateCallback | undefined) => FormValidationResult;
            scrollToField: (prop: FormItemProp) => void;
        }, {}, {}, {}, {
            readonly disabled: boolean;
            readonly labelPosition: EpPropMergeType<StringConstructor, "top" | "right" | "left", unknown>;
            readonly requireAsteriskPosition: EpPropMergeType<StringConstructor, "right" | "left", unknown>;
            readonly labelWidth: EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown>;
            readonly labelSuffix: string;
            readonly showMessage: EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly validateOnRuleChange: EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly inline: boolean;
            readonly inlineMessage: boolean;
            readonly statusIcon: boolean;
            readonly hideRequiredAsterisk: boolean;
            readonly scrollToError: boolean;
        }> | null>;
        handleSubmit: () => Promise<boolean>;
        handleReset: () => void;
    }, unknown, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
        "update:modelValue": (values: FieldValues) => void;
        submit: (values: FieldValues) => void;
        change: (values: FieldValues, column: PlusColumn) => void;
        reset: (values: FieldValues) => void;
        submitError: (errors: unknown) => void;
        validate: (...args: any[]) => void;
    }, VNodeProps & AllowedComponentProps & ComponentCustomProps & Readonly< ExtractPropTypes<{
        columns: {
            type: PropType< PlusColumn[]>;
            default: () => never[];
        };
        group: {
            type: PropType<false | PlusFormGroupRow[]>;
            default: boolean;
        };
        modelValue: {
            type: PropType<FieldValues>;
            default: () => {};
        };
        rules: {
            type: PropType<Partial<Record<string, Arrayable<FormItemRule>>>>;
            default: () => {};
        };
        labelPosition: {
            type: PropType<"top" | "right" | "left">;
            default: string;
        };
        labelWidth: {
            type: PropType<string | number>;
            default: string;
        };
        labelSuffix: {
            type: PropType<string>;
            default: string;
        };
        hasLabel: {
            type: PropType<boolean>;
            default: boolean;
        };
        defaultValues: {
            type: PropType<FieldValues>;
            default: () => {};
        };
        rowProps: {
            type: PropType<Partial< Mutable<RowProps>>>;
            default: () => {};
        };
        colProps: {
            type: PropType<Partial< Mutable<ColProps>>>;
            default: () => {};
        };
        hasErrorTip: {
            type: PropType<boolean>;
            default: boolean;
        };
        hasFooter: {
            type: PropType<boolean>;
            default: boolean;
        };
        hasReset: {
            type: PropType<boolean>;
            default: boolean;
        };
        submitText: {
            type: PropType<string>;
            default: string;
        };
        resetText: {
            type: PropType<string>;
            default: string;
        };
        submitLoading: {
            type: PropType<boolean>;
            default: boolean;
        };
        footerAlign: {
            type: PropType<"right" | "left" | "center">;
            default: string;
        };
        cardProps: {
            type: PropType<Partial< Mutable<CardProps>>>;
            default: () => {};
        };
        prevent: {
            type: PropType<boolean>;
            default: boolean;
        };
        collapseDuration: {
            type: PropType<number>;
            default: undefined;
        };
        collapseTransition: {
            type: PropType<boolean>;
            default: undefined;
        };
        clearable: {
            type: PropType<boolean>;
            default: boolean;
        };
    }>> & {
        onChange?: ((values: FieldValues, column: PlusColumn) => any) | undefined;
        "onUpdate:modelValue"?: ((values: FieldValues) => any) | undefined;
        onReset?: ((values: FieldValues) => any) | undefined;
        onSubmit?: ((values: FieldValues) => any) | undefined;
        onValidate?: ((...args: any[]) => any) | undefined;
        onSubmitError?: ((errors: unknown) => any) | undefined;
    }, {
        columns: PlusColumn[];
        group: false | PlusFormGroupRow[];
        modelValue: FieldValues;
        rules: Partial<Record<string, Arrayable<FormItemRule>>>;
        labelPosition: "top" | "right" | "left";
        labelWidth: string | number;
        labelSuffix: string;
        hasLabel: boolean;
        defaultValues: FieldValues;
        rowProps: Partial< Mutable<RowProps>>;
        colProps: Partial< Mutable<ColProps>>;
        hasErrorTip: boolean;
        hasFooter: boolean;
        hasReset: boolean;
        submitText: string;
        resetText: string;
        submitLoading: boolean;
        footerAlign: "right" | "left" | "center";
        cardProps: Partial< Mutable<CardProps>>;
        prevent: boolean;
        collapseDuration: number;
        collapseTransition: boolean;
        clearable: boolean;
    }, true, {}, {}, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly< ExtractPropTypes<{
        columns: {
            type: PropType< PlusColumn[]>;
            default: () => never[];
        };
        group: {
            type: PropType<false | PlusFormGroupRow[]>;
            default: boolean;
        };
        modelValue: {
            type: PropType<FieldValues>;
            default: () => {};
        };
        rules: {
            type: PropType<Partial<Record<string, Arrayable<FormItemRule>>>>;
            default: () => {};
        };
        labelPosition: {
            type: PropType<"top" | "right" | "left">;
            default: string;
        };
        labelWidth: {
            type: PropType<string | number>;
            default: string;
        };
        labelSuffix: {
            type: PropType<string>;
            default: string;
        };
        hasLabel: {
            type: PropType<boolean>;
            default: boolean;
        };
        defaultValues: {
            type: PropType<FieldValues>;
            default: () => {};
        };
        rowProps: {
            type: PropType<Partial< Mutable<RowProps>>>;
            default: () => {};
        };
        colProps: {
            type: PropType<Partial< Mutable<ColProps>>>;
            default: () => {};
        };
        hasErrorTip: {
            type: PropType<boolean>;
            default: boolean;
        };
        hasFooter: {
            type: PropType<boolean>;
            default: boolean;
        };
        hasReset: {
            type: PropType<boolean>;
            default: boolean;
        };
        submitText: {
            type: PropType<string>;
            default: string;
        };
        resetText: {
            type: PropType<string>;
            default: string;
        };
        submitLoading: {
            type: PropType<boolean>;
            default: boolean;
        };
        footerAlign: {
            type: PropType<"right" | "left" | "center">;
            default: string;
        };
        cardProps: {
            type: PropType<Partial< Mutable<CardProps>>>;
            default: () => {};
        };
        prevent: {
            type: PropType<boolean>;
            default: boolean;
        };
        collapseDuration: {
            type: PropType<number>;
            default: undefined;
        };
        collapseTransition: {
            type: PropType<boolean>;
            default: undefined;
        };
        clearable: {
            type: PropType<boolean>;
            default: boolean;
        };
    }>> & {
        onChange?: ((values: FieldValues, column: PlusColumn) => any) | undefined;
        "onUpdate:modelValue"?: ((values: FieldValues) => any) | undefined;
        onReset?: ((values: FieldValues) => any) | undefined;
        onSubmit?: ((values: FieldValues) => any) | undefined;
        onValidate?: ((...args: any[]) => any) | undefined;
        onSubmitError?: ((errors: unknown) => any) | undefined;
    }, {
        formInstance: Ref< CreateComponentPublicInstance<Readonly< ExtractPropTypes<{
            readonly model: ObjectConstructor;
            readonly rules: {
                readonly type: PropType<Partial<Record<string, Arrayable<FormItemRule>>>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly labelPosition: EpPropFinalized<StringConstructor, "top" | "right" | "left", unknown, "right", boolean>;
            readonly requireAsteriskPosition: EpPropFinalized<StringConstructor, "right" | "left", unknown, "left", boolean>;
            readonly labelWidth: EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
            readonly labelSuffix: EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
            readonly inline: BooleanConstructor;
            readonly inlineMessage: BooleanConstructor;
            readonly statusIcon: BooleanConstructor;
            readonly showMessage: EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly validateOnRuleChange: EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly hideRequiredAsterisk: BooleanConstructor;
            readonly scrollToError: BooleanConstructor;
            readonly scrollIntoViewOptions: {
                readonly type: PropType<EpPropMergeType<readonly [ObjectConstructor, BooleanConstructor], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly size: {
                readonly type: PropType<EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly disabled: BooleanConstructor;
        }>> & {
            onValidate?: ((prop: FormItemProp, isValid: boolean, message: string) => any) | undefined;
        }, {
            COMPONENT_NAME: string;
            props: Readonly< LooseRequired<Readonly< ExtractPropTypes<{
                readonly model: ObjectConstructor;
                readonly rules: {
                    readonly type: PropType<Partial<Record<string, Arrayable<FormItemRule>>>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly labelPosition: EpPropFinalized<StringConstructor, "top" | "right" | "left", unknown, "right", boolean>;
                readonly requireAsteriskPosition: EpPropFinalized<StringConstructor, "right" | "left", unknown, "left", boolean>;
                readonly labelWidth: EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
                readonly labelSuffix: EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
                readonly inline: BooleanConstructor;
                readonly inlineMessage: BooleanConstructor;
                readonly statusIcon: BooleanConstructor;
                readonly showMessage: EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
                readonly validateOnRuleChange: EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
                readonly hideRequiredAsterisk: BooleanConstructor;
                readonly scrollToError: BooleanConstructor;
                readonly scrollIntoViewOptions: {
                    readonly type: PropType<EpPropMergeType<readonly [ObjectConstructor, BooleanConstructor], unknown, unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly size: {
                    readonly type: PropType<EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly disabled: BooleanConstructor;
            }>> & {
                onValidate?: ((prop: FormItemProp, isValid: boolean, message: string) => any) | undefined;
            }>>;
            emit: (event: "validate", prop: FormItemProp, isValid: boolean, message: string) => void;
            fields: FormItemContext[];
            formSize: ComputedRef<"" | "default" | "small" | "large">;
            ns: {
                namespace: ComputedRef<string>;
                b: (blockSuffix?: string | undefined) => string;
                e: (element?: string | undefined) => string;
                m: (modifier?: string | undefined) => string;
                be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                em: (element?: string | undefined, modifier?: string | undefined) => string;
                bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                is: {
                    (name: string, state: boolean | undefined): string;
                    (name: string): string;
                };
                cssVar: (object: Record<string, string>) => Record<string, string>;
                cssVarName: (name: string) => string;
                cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                cssVarBlockName: (name: string) => string;
            };
            formClasses: ComputedRef<(string | {
                [x: string]: boolean | EpPropMergeType<StringConstructor, "top" | "right" | "left", unknown>;
            })[]>;
            getField: (prop: string) => FormItemContext | undefined;
            addField: (field: FormItemContext) => void;
            removeField: (field: FormItemContext) => void;
            resetFields: (props?: Arrayable<FormItemProp> | undefined) => void;
            clearValidate: (props?: Arrayable<FormItemProp> | undefined) => void;
            isValidatable: ComputedRef<boolean>;
            obtainValidateFields: (props: Arrayable<FormItemProp>) => FormItemContext[];
            validate: (callback?: FormValidateCallback | undefined) => FormValidationResult;
            doValidateField: (props?: Arrayable<FormItemProp> | undefined) => Promise<boolean>;
            validateField: (props?: Arrayable<FormItemProp> | undefined, callback?: FormValidateCallback | undefined) => FormValidationResult;
            scrollToField: (prop: FormItemProp) => void;
        }, unknown, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
            validate: (prop: FormItemProp, isValid: boolean, message: string) => boolean;
        }, VNodeProps & AllowedComponentProps & ComponentCustomProps & Readonly< ExtractPropTypes<{
            readonly model: ObjectConstructor;
            readonly rules: {
                readonly type: PropType<Partial<Record<string, Arrayable<FormItemRule>>>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly labelPosition: EpPropFinalized<StringConstructor, "top" | "right" | "left", unknown, "right", boolean>;
            readonly requireAsteriskPosition: EpPropFinalized<StringConstructor, "right" | "left", unknown, "left", boolean>;
            readonly labelWidth: EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
            readonly labelSuffix: EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
            readonly inline: BooleanConstructor;
            readonly inlineMessage: BooleanConstructor;
            readonly statusIcon: BooleanConstructor;
            readonly showMessage: EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly validateOnRuleChange: EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly hideRequiredAsterisk: BooleanConstructor;
            readonly scrollToError: BooleanConstructor;
            readonly scrollIntoViewOptions: {
                readonly type: PropType<EpPropMergeType<readonly [ObjectConstructor, BooleanConstructor], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly size: {
                readonly type: PropType<EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly disabled: BooleanConstructor;
        }>> & {
            onValidate?: ((prop: FormItemProp, isValid: boolean, message: string) => any) | undefined;
        }, {
            readonly disabled: boolean;
            readonly labelPosition: EpPropMergeType<StringConstructor, "top" | "right" | "left", unknown>;
            readonly requireAsteriskPosition: EpPropMergeType<StringConstructor, "right" | "left", unknown>;
            readonly labelWidth: EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown>;
            readonly labelSuffix: string;
            readonly showMessage: EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly validateOnRuleChange: EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly inline: boolean;
            readonly inlineMessage: boolean;
            readonly statusIcon: boolean;
            readonly hideRequiredAsterisk: boolean;
            readonly scrollToError: boolean;
        }, true, {}, {}, {
            P: {};
            B: {};
            D: {};
            C: {};
            M: {};
            Defaults: {};
        }, Readonly< ExtractPropTypes<{
            readonly model: ObjectConstructor;
            readonly rules: {
                readonly type: PropType<Partial<Record<string, Arrayable<FormItemRule>>>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly labelPosition: EpPropFinalized<StringConstructor, "top" | "right" | "left", unknown, "right", boolean>;
            readonly requireAsteriskPosition: EpPropFinalized<StringConstructor, "right" | "left", unknown, "left", boolean>;
            readonly labelWidth: EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
            readonly labelSuffix: EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
            readonly inline: BooleanConstructor;
            readonly inlineMessage: BooleanConstructor;
            readonly statusIcon: BooleanConstructor;
            readonly showMessage: EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly validateOnRuleChange: EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly hideRequiredAsterisk: BooleanConstructor;
            readonly scrollToError: BooleanConstructor;
            readonly scrollIntoViewOptions: {
                readonly type: PropType<EpPropMergeType<readonly [ObjectConstructor, BooleanConstructor], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly size: {
                readonly type: PropType<EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly disabled: BooleanConstructor;
        }>> & {
            onValidate?: ((prop: FormItemProp, isValid: boolean, message: string) => any) | undefined;
        }, {
            COMPONENT_NAME: string;
            props: Readonly< LooseRequired<Readonly< ExtractPropTypes<{
                readonly model: ObjectConstructor;
                readonly rules: {
                    readonly type: PropType<Partial<Record<string, Arrayable<FormItemRule>>>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly labelPosition: EpPropFinalized<StringConstructor, "top" | "right" | "left", unknown, "right", boolean>;
                readonly requireAsteriskPosition: EpPropFinalized<StringConstructor, "right" | "left", unknown, "left", boolean>;
                readonly labelWidth: EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
                readonly labelSuffix: EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
                readonly inline: BooleanConstructor;
                readonly inlineMessage: BooleanConstructor;
                readonly statusIcon: BooleanConstructor;
                readonly showMessage: EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
                readonly validateOnRuleChange: EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
                readonly hideRequiredAsterisk: BooleanConstructor;
                readonly scrollToError: BooleanConstructor;
                readonly scrollIntoViewOptions: {
                    readonly type: PropType<EpPropMergeType<readonly [ObjectConstructor, BooleanConstructor], unknown, unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly size: {
                    readonly type: PropType<EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly disabled: BooleanConstructor;
            }>> & {
                onValidate?: ((prop: FormItemProp, isValid: boolean, message: string) => any) | undefined;
            }>>;
            emit: (event: "validate", prop: FormItemProp, isValid: boolean, message: string) => void;
            fields: FormItemContext[];
            formSize: ComputedRef<"" | "default" | "small" | "large">;
            ns: {
                namespace: ComputedRef<string>;
                b: (blockSuffix?: string | undefined) => string;
                e: (element?: string | undefined) => string;
                m: (modifier?: string | undefined) => string;
                be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                em: (element?: string | undefined, modifier?: string | undefined) => string;
                bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                is: {
                    (name: string, state: boolean | undefined): string;
                    (name: string): string;
                };
                cssVar: (object: Record<string, string>) => Record<string, string>;
                cssVarName: (name: string) => string;
                cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                cssVarBlockName: (name: string) => string;
            };
            formClasses: ComputedRef<(string | {
                [x: string]: boolean | EpPropMergeType<StringConstructor, "top" | "right" | "left", unknown>;
            })[]>;
            getField: (prop: string) => FormItemContext | undefined;
            addField: (field: FormItemContext) => void;
            removeField: (field: FormItemContext) => void;
            resetFields: (props?: Arrayable<FormItemProp> | undefined) => void;
            clearValidate: (props?: Arrayable<FormItemProp> | undefined) => void;
            isValidatable: ComputedRef<boolean>;
            obtainValidateFields: (props: Arrayable<FormItemProp>) => FormItemContext[];
            validate: (callback?: FormValidateCallback | undefined) => FormValidationResult;
            doValidateField: (props?: Arrayable<FormItemProp> | undefined) => Promise<boolean>;
            validateField: (props?: Arrayable<FormItemProp> | undefined, callback?: FormValidateCallback | undefined) => FormValidationResult;
            scrollToField: (prop: FormItemProp) => void;
        }, {}, {}, {}, {
            readonly disabled: boolean;
            readonly labelPosition: EpPropMergeType<StringConstructor, "top" | "right" | "left", unknown>;
            readonly requireAsteriskPosition: EpPropMergeType<StringConstructor, "right" | "left", unknown>;
            readonly labelWidth: EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown>;
            readonly labelSuffix: string;
            readonly showMessage: EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly validateOnRuleChange: EpPropMergeType<BooleanConstructor, unknown, unknown>;
            readonly inline: boolean;
            readonly inlineMessage: boolean;
            readonly statusIcon: boolean;
            readonly hideRequiredAsterisk: boolean;
            readonly scrollToError: boolean;
        }> | null>;
        handleSubmit: () => Promise<boolean>;
        handleReset: () => void;
    }, {}, {}, {}, {
        columns: PlusColumn[];
        group: false | PlusFormGroupRow[];
        modelValue: FieldValues;
        rules: Partial<Record<string, Arrayable<FormItemRule>>>;
        labelPosition: "top" | "right" | "left";
        labelWidth: string | number;
        labelSuffix: string;
        hasLabel: boolean;
        defaultValues: FieldValues;
        rowProps: Partial< Mutable<RowProps>>;
        colProps: Partial< Mutable<ColProps>>;
        hasErrorTip: boolean;
        hasFooter: boolean;
        hasReset: boolean;
        submitText: string;
        resetText: string;
        submitLoading: boolean;
        footerAlign: "right" | "left" | "center";
        cardProps: Partial< Mutable<CardProps>>;
        prevent: boolean;
        collapseDuration: number;
        collapseTransition: boolean;
        clearable: boolean;
    }>;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
} & ComponentOptionsBase<Readonly< ExtractPropTypes<{
    columns: {
        type: PropType< PlusColumn[]>;
        default: () => never[];
    };
    group: {
        type: PropType<false | PlusFormGroupRow[]>;
        default: boolean;
    };
    modelValue: {
        type: PropType<FieldValues>;
        default: () => {};
    };
    rules: {
        type: PropType<Partial<Record<string, Arrayable<FormItemRule>>>>;
        default: () => {};
    };
    labelPosition: {
        type: PropType<"top" | "right" | "left">;
        default: string;
    };
    labelWidth: {
        type: PropType<string | number>;
        default: string;
    };
    labelSuffix: {
        type: PropType<string>;
        default: string;
    };
    hasLabel: {
        type: PropType<boolean>;
        default: boolean;
    };
    defaultValues: {
        type: PropType<FieldValues>;
        default: () => {};
    };
    rowProps: {
        type: PropType<Partial< Mutable<RowProps>>>;
        default: () => {};
    };
    colProps: {
        type: PropType<Partial< Mutable<ColProps>>>;
        default: () => {};
    };
    hasErrorTip: {
        type: PropType<boolean>;
        default: boolean;
    };
    hasFooter: {
        type: PropType<boolean>;
        default: boolean;
    };
    hasReset: {
        type: PropType<boolean>;
        default: boolean;
    };
    submitText: {
        type: PropType<string>;
        default: string;
    };
    resetText: {
        type: PropType<string>;
        default: string;
    };
    submitLoading: {
        type: PropType<boolean>;
        default: boolean;
    };
    footerAlign: {
        type: PropType<"right" | "left" | "center">;
        default: string;
    };
    cardProps: {
        type: PropType<Partial< Mutable<CardProps>>>;
        default: () => {};
    };
    prevent: {
        type: PropType<boolean>;
        default: boolean;
    };
    collapseDuration: {
        type: PropType<number>;
        default: undefined;
    };
    collapseTransition: {
        type: PropType<boolean>;
        default: undefined;
    };
    clearable: {
        type: PropType<boolean>;
        default: boolean;
    };
}>> & {
    onChange?: ((values: FieldValues, column: PlusColumn) => any) | undefined;
    "onUpdate:modelValue"?: ((values: FieldValues) => any) | undefined;
    onReset?: ((values: FieldValues) => any) | undefined;
    onSubmit?: ((values: FieldValues) => any) | undefined;
    onValidate?: ((...args: any[]) => any) | undefined;
    onSubmitError?: ((errors: unknown) => any) | undefined;
}, {
    formInstance: Ref< CreateComponentPublicInstance<Readonly< ExtractPropTypes<{
        readonly model: ObjectConstructor;
        readonly rules: {
            readonly type: PropType<Partial<Record<string, Arrayable<FormItemRule>>>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly labelPosition: EpPropFinalized<StringConstructor, "top" | "right" | "left", unknown, "right", boolean>;
        readonly requireAsteriskPosition: EpPropFinalized<StringConstructor, "right" | "left", unknown, "left", boolean>;
        readonly labelWidth: EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
        readonly labelSuffix: EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
        readonly inline: BooleanConstructor;
        readonly inlineMessage: BooleanConstructor;
        readonly statusIcon: BooleanConstructor;
        readonly showMessage: EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly validateOnRuleChange: EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly hideRequiredAsterisk: BooleanConstructor;
        readonly scrollToError: BooleanConstructor;
        readonly scrollIntoViewOptions: {
            readonly type: PropType<EpPropMergeType<readonly [ObjectConstructor, BooleanConstructor], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly size: {
            readonly type: PropType<EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly disabled: BooleanConstructor;
    }>> & {
        onValidate?: ((prop: FormItemProp, isValid: boolean, message: string) => any) | undefined;
    }, {
        COMPONENT_NAME: string;
        props: Readonly< LooseRequired<Readonly< ExtractPropTypes<{
            readonly model: ObjectConstructor;
            readonly rules: {
                readonly type: PropType<Partial<Record<string, Arrayable<FormItemRule>>>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly labelPosition: EpPropFinalized<StringConstructor, "top" | "right" | "left", unknown, "right", boolean>;
            readonly requireAsteriskPosition: EpPropFinalized<StringConstructor, "right" | "left", unknown, "left", boolean>;
            readonly labelWidth: EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
            readonly labelSuffix: EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
            readonly inline: BooleanConstructor;
            readonly inlineMessage: BooleanConstructor;
            readonly statusIcon: BooleanConstructor;
            readonly showMessage: EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly validateOnRuleChange: EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly hideRequiredAsterisk: BooleanConstructor;
            readonly scrollToError: BooleanConstructor;
            readonly scrollIntoViewOptions: {
                readonly type: PropType<EpPropMergeType<readonly [ObjectConstructor, BooleanConstructor], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly size: {
                readonly type: PropType<EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly disabled: BooleanConstructor;
        }>> & {
            onValidate?: ((prop: FormItemProp, isValid: boolean, message: string) => any) | undefined;
        }>>;
        emit: (event: "validate", prop: FormItemProp, isValid: boolean, message: string) => void;
        fields: FormItemContext[];
        formSize: ComputedRef<"" | "default" | "small" | "large">;
        ns: {
            namespace: ComputedRef<string>;
            b: (blockSuffix?: string | undefined) => string;
            e: (element?: string | undefined) => string;
            m: (modifier?: string | undefined) => string;
            be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
            em: (element?: string | undefined, modifier?: string | undefined) => string;
            bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
            bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
        formClasses: ComputedRef<(string | {
            [x: string]: boolean | EpPropMergeType<StringConstructor, "top" | "right" | "left", unknown>;
        })[]>;
        getField: (prop: string) => FormItemContext | undefined;
        addField: (field: FormItemContext) => void;
        removeField: (field: FormItemContext) => void;
        resetFields: (props?: Arrayable<FormItemProp> | undefined) => void;
        clearValidate: (props?: Arrayable<FormItemProp> | undefined) => void;
        isValidatable: ComputedRef<boolean>;
        obtainValidateFields: (props: Arrayable<FormItemProp>) => FormItemContext[];
        validate: (callback?: FormValidateCallback | undefined) => FormValidationResult;
        doValidateField: (props?: Arrayable<FormItemProp> | undefined) => Promise<boolean>;
        validateField: (props?: Arrayable<FormItemProp> | undefined, callback?: FormValidateCallback | undefined) => FormValidationResult;
        scrollToField: (prop: FormItemProp) => void;
    }, unknown, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
        validate: (prop: FormItemProp, isValid: boolean, message: string) => boolean;
    }, VNodeProps & AllowedComponentProps & ComponentCustomProps & Readonly< ExtractPropTypes<{
        readonly model: ObjectConstructor;
        readonly rules: {
            readonly type: PropType<Partial<Record<string, Arrayable<FormItemRule>>>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly labelPosition: EpPropFinalized<StringConstructor, "top" | "right" | "left", unknown, "right", boolean>;
        readonly requireAsteriskPosition: EpPropFinalized<StringConstructor, "right" | "left", unknown, "left", boolean>;
        readonly labelWidth: EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
        readonly labelSuffix: EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
        readonly inline: BooleanConstructor;
        readonly inlineMessage: BooleanConstructor;
        readonly statusIcon: BooleanConstructor;
        readonly showMessage: EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly validateOnRuleChange: EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly hideRequiredAsterisk: BooleanConstructor;
        readonly scrollToError: BooleanConstructor;
        readonly scrollIntoViewOptions: {
            readonly type: PropType<EpPropMergeType<readonly [ObjectConstructor, BooleanConstructor], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly size: {
            readonly type: PropType<EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly disabled: BooleanConstructor;
    }>> & {
        onValidate?: ((prop: FormItemProp, isValid: boolean, message: string) => any) | undefined;
    }, {
        readonly disabled: boolean;
        readonly labelPosition: EpPropMergeType<StringConstructor, "top" | "right" | "left", unknown>;
        readonly requireAsteriskPosition: EpPropMergeType<StringConstructor, "right" | "left", unknown>;
        readonly labelWidth: EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown>;
        readonly labelSuffix: string;
        readonly showMessage: EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly validateOnRuleChange: EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly inline: boolean;
        readonly inlineMessage: boolean;
        readonly statusIcon: boolean;
        readonly hideRequiredAsterisk: boolean;
        readonly scrollToError: boolean;
    }, true, {}, {}, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly< ExtractPropTypes<{
        readonly model: ObjectConstructor;
        readonly rules: {
            readonly type: PropType<Partial<Record<string, Arrayable<FormItemRule>>>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly labelPosition: EpPropFinalized<StringConstructor, "top" | "right" | "left", unknown, "right", boolean>;
        readonly requireAsteriskPosition: EpPropFinalized<StringConstructor, "right" | "left", unknown, "left", boolean>;
        readonly labelWidth: EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
        readonly labelSuffix: EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
        readonly inline: BooleanConstructor;
        readonly inlineMessage: BooleanConstructor;
        readonly statusIcon: BooleanConstructor;
        readonly showMessage: EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly validateOnRuleChange: EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
        readonly hideRequiredAsterisk: BooleanConstructor;
        readonly scrollToError: BooleanConstructor;
        readonly scrollIntoViewOptions: {
            readonly type: PropType<EpPropMergeType<readonly [ObjectConstructor, BooleanConstructor], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly size: {
            readonly type: PropType<EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly disabled: BooleanConstructor;
    }>> & {
        onValidate?: ((prop: FormItemProp, isValid: boolean, message: string) => any) | undefined;
    }, {
        COMPONENT_NAME: string;
        props: Readonly< LooseRequired<Readonly< ExtractPropTypes<{
            readonly model: ObjectConstructor;
            readonly rules: {
                readonly type: PropType<Partial<Record<string, Arrayable<FormItemRule>>>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly labelPosition: EpPropFinalized<StringConstructor, "top" | "right" | "left", unknown, "right", boolean>;
            readonly requireAsteriskPosition: EpPropFinalized<StringConstructor, "right" | "left", unknown, "left", boolean>;
            readonly labelWidth: EpPropFinalized<readonly [StringConstructor, NumberConstructor], unknown, unknown, "", boolean>;
            readonly labelSuffix: EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
            readonly inline: BooleanConstructor;
            readonly inlineMessage: BooleanConstructor;
            readonly statusIcon: BooleanConstructor;
            readonly showMessage: EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly validateOnRuleChange: EpPropFinalized<BooleanConstructor, unknown, unknown, true, boolean>;
            readonly hideRequiredAsterisk: BooleanConstructor;
            readonly scrollToError: BooleanConstructor;
            readonly scrollIntoViewOptions: {
                readonly type: PropType<EpPropMergeType<readonly [ObjectConstructor, BooleanConstructor], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly size: {
                readonly type: PropType<EpPropMergeType<StringConstructor, "" | "default" | "small" | "large", unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly disabled: BooleanConstructor;
        }>> & {
            onValidate?: ((prop: FormItemProp, isValid: boolean, message: string) => any) | undefined;
        }>>;
        emit: (event: "validate", prop: FormItemProp, isValid: boolean, message: string) => void;
        fields: FormItemContext[];
        formSize: ComputedRef<"" | "default" | "small" | "large">;
        ns: {
            namespace: ComputedRef<string>;
            b: (blockSuffix?: string | undefined) => string;
            e: (element?: string | undefined) => string;
            m: (modifier?: string | undefined) => string;
            be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
            em: (element?: string | undefined, modifier?: string | undefined) => string;
            bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
            bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
        formClasses: ComputedRef<(string | {
            [x: string]: boolean | EpPropMergeType<StringConstructor, "top" | "right" | "left", unknown>;
        })[]>;
        getField: (prop: string) => FormItemContext | undefined;
        addField: (field: FormItemContext) => void;
        removeField: (field: FormItemContext) => void;
        resetFields: (props?: Arrayable<FormItemProp> | undefined) => void;
        clearValidate: (props?: Arrayable<FormItemProp> | undefined) => void;
        isValidatable: ComputedRef<boolean>;
        obtainValidateFields: (props: Arrayable<FormItemProp>) => FormItemContext[];
        validate: (callback?: FormValidateCallback | undefined) => FormValidationResult;
        doValidateField: (props?: Arrayable<FormItemProp> | undefined) => Promise<boolean>;
        validateField: (props?: Arrayable<FormItemProp> | undefined, callback?: FormValidateCallback | undefined) => FormValidationResult;
        scrollToField: (prop: FormItemProp) => void;
    }, {}, {}, {}, {
        readonly disabled: boolean;
        readonly labelPosition: EpPropMergeType<StringConstructor, "top" | "right" | "left", unknown>;
        readonly requireAsteriskPosition: EpPropMergeType<StringConstructor, "right" | "left", unknown>;
        readonly labelWidth: EpPropMergeType<readonly [StringConstructor, NumberConstructor], unknown, unknown>;
        readonly labelSuffix: string;
        readonly showMessage: EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly validateOnRuleChange: EpPropMergeType<BooleanConstructor, unknown, unknown>;
        readonly inline: boolean;
        readonly inlineMessage: boolean;
        readonly statusIcon: boolean;
        readonly hideRequiredAsterisk: boolean;
        readonly scrollToError: boolean;
    }> | null>;
    handleSubmit: () => Promise<boolean>;
    handleReset: () => void;
}, unknown, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
    "update:modelValue": (values: FieldValues) => void;
    submit: (values: FieldValues) => void;
    change: (values: FieldValues, column: PlusColumn) => void;
    reset: (values: FieldValues) => void;
    submitError: (errors: unknown) => void;
    validate: (...args: any[]) => void;
}, string, {
    columns: PlusColumn[];
    group: false | PlusFormGroupRow[];
    modelValue: FieldValues;
    rules: Partial<Record<string, Arrayable<FormItemRule>>>;
    labelPosition: "top" | "right" | "left";
    labelWidth: string | number;
    labelSuffix: string;
    hasLabel: boolean;
    defaultValues: FieldValues;
    rowProps: Partial< Mutable<RowProps>>;
    colProps: Partial< Mutable<ColProps>>;
    hasErrorTip: boolean;
    hasFooter: boolean;
    hasReset: boolean;
    submitText: string;
    resetText: string;
    submitLoading: boolean;
    footerAlign: "right" | "left" | "center";
    cardProps: Partial< Mutable<CardProps>>;
    prevent: boolean;
    collapseDuration: number;
    collapseTransition: boolean;
    clearable: boolean;
}, {}, string, {}> & VNodeProps & AllowedComponentProps & ComponentCustomProps & (new () => {
    $slots: Partial<Record<NonNullable<string | number>, (_: {
        [x: string]: any;
        label?: string | ComputedRef<string> | undefined;
        prop: string;
        width?: string | number | undefined;
        minWidth?: string | number | undefined;
        editable?: boolean | undefined;
        valueType?: TableValueType | FormItemValueType;
        hideInDescriptions?: boolean | Ref<boolean> | ComputedRef<boolean> | undefined;
        hideInForm?: boolean | Ref<boolean> | ComputedRef<boolean> | undefined;
        hideInTable?: boolean | Ref<boolean> | ComputedRef<boolean> | undefined;
        hideInSearch?: boolean | Ref<boolean> | ComputedRef<boolean> | undefined;
        descriptionsItemProps?: RecordType | undefined;
        options?: OptionsType | undefined;
        optionsMap?: {
            label?: string | undefined;
            value?: string | undefined;
        } | undefined;
        customGetStatus?: ((data: {
            options: OptionsRow<undefined>[];
            value: string | number;
            row: RecordType;
        }) => OptionsRow<undefined>) | undefined;
        tooltip?: string | Partial< ElTooltipProps> | ComputedRef<string> | ComputedRef<Partial< ElTooltipProps>> | undefined;
        render?: ((value: any, data: {
            row: RecordType;
            column: PlusColumn;
            index: number;
        }) => RenderTypes) | undefined;
        renderHTML?: ((value: any, data: {
            row: RecordType;
            column: PlusColumn;
            index: number;
        }) => string) | undefined;
        renderHeader?: ((label: string, props: PlusColumn) => RenderTypes) | undefined;
        renderDescriptionsItem?: ((data: {
            value: string;
            column: PlusColumn;
            row: RecordType;
        }) => RenderTypes) | undefined;
        renderDescriptionsLabel?: ((data: {
            label: string;
            column: PlusColumn;
            row: RecordType;
        }) => RenderTypes) | undefined;
        order?: number | ComputedRef<number> | undefined;
        children?: PlusColumn[] | undefined;
        headerFilter?: boolean | undefined;
        disabledHeaderFilter?: boolean | undefined;
        headerIsChecked?: boolean | undefined;
        tableColumnProps?: Partial<Omit< TableColumnCtx<any>, "label " | "prop" | "width" | "minWidth"> & {
            [key: string]: any;
        }> | undefined;
        preview?: boolean | undefined;
        linkText?: string | undefined;
        formatter?: ((value: any, data: {
            row: RecordType;
            column: PlusColumn;
            index: number;
        }) => string | number) | undefined;
        formProps?: Partial< PlusFormProps> | ComputedRef<Partial< PlusFormProps>> | ((value: FieldValueType, data: {
            row: Record<string, any>;
            index: number;
        }) => Partial< PlusFormProps>) | undefined;
        formItemProps?: PropsItemType< Mutable<FormItemProps> & {
            [key: string]: any;
            style?: CSSProperties | undefined;
        }> | undefined;
        fieldProps?: PropsItemType<Partial<{
            [key: string]: any;
            style: CSSProperties;
            rows: number;
            autocomplete: string;
            type: "" | "number" | "default" | "search" | "checkbox" | "radio" | "textarea" | "text" | "circle" | "color" | "button" | "success" | "warning" | "info" | "primary" | "danger" | "reset" | "submit" | "time" | "image" | "line" | "date" | "year" | "years" | "month" | "dates" | "week" | "datetime" | "datetimerange" | "daterange" | "monthrange" | "range" | "dashboard" | "hidden" | "datetime-local" | "email" | "file" | "password" | "tel" | "url";
            loading: EpPropMergeType<BooleanConstructor, unknown, unknown> | EpPropMergeType<StringConstructor, "lazy" | "eager", unknown> | undefined;
            step: string | number;
            format: string | ProgressFn | undefined;
            filterMethod: Function | ((node: CascaderNode, keyword: string) => boolean) | undefined;
            id: string | [string, string];
            effect: string;
            height: string | number;
            autosize: InputAutoSize;
        } & Mutable<Omit< AutocompleteProps, OmitTypes> & Omit< CascaderProps, OmitTypes> & Omit< CheckboxGroupProps, OmitTypes> & Omit< ColorPickerProps, OmitTypes> & Omit< DatePickerProps, OmitTypes> & Omit< InputProps, OmitTypes> & Omit< InputNumberProps, OmitTypes> & Omit< RadioGroupProps, OmitTypes> & Omit< RateProps, OmitTypes> & Omit< ISelectProps, OmitTypes> & Omit< SliderProps, OmitTypes> & Omit< SwitchProps, OmitTypes> & Omit< TimePickerDefaultProps, OmitTypes> & Omit< TimeSelectProps, OmitTypes> & Omit< PlusRadioProps, OmitTypes> & Omit< PlusDatePickerProps, OmitTypes> & Omit< PlusInputTagProps, OmitTypes> & Omit< TextProps, OmitTypes> & Omit< ImageProps, OmitTypes> & Omit< LinkProps, OmitTypes> & Omit< TagProps, OmitTypes> & Omit< ProgressProps, OmitTypes>>>> | undefined;
        renderField?: ((value: FieldValueType, onChange: (value: FieldValueType) => void, props: PlusColumn) => RenderTypes) | undefined;
        colProps?: Partial< Mutable<ColProps> & {
            [key: string]: any;
            style?: CSSProperties | undefined;
        }> | undefined;
        hasLabel?: boolean | Ref<boolean> | ComputedRef<boolean> | undefined;
        renderLabel?: ((label: string, props: PlusColumn) => RenderTypes) | undefined;
        renderExtra?: ((column: PlusColumn) => RenderTypes) | undefined;
        fieldSlots?: {
            [slotName: string]: (data?: any) => RenderTypes;
        } | undefined;
        fieldChildrenSlot?: ((option?: OptionsRow<undefined> | undefined) => RenderTypes) | undefined;
        renderErrorMessage?: ((props: PlusColumn & {
            value?: FieldValueType;
            error?: string | undefined;
            label?: string | undefined;
        }) => RenderTypes) | undefined;
    }) => any>> & Partial<Record<NonNullable<string | number>, (_: {
        [x: string]: any;
        label?: string | ComputedRef<string> | undefined;
        prop: string;
        width?: string | number | undefined;
        minWidth?: string | number | undefined;
        editable?: boolean | undefined;
        valueType?: TableValueType | FormItemValueType;
        hideInDescriptions?: boolean | Ref<boolean> | ComputedRef<boolean> | undefined;
        hideInForm?: boolean | Ref<boolean> | ComputedRef<boolean> | undefined;
        hideInTable?: boolean | Ref<boolean> | ComputedRef<boolean> | undefined;
        hideInSearch?: boolean | Ref<boolean> | ComputedRef<boolean> | undefined;
        descriptionsItemProps?: RecordType | undefined;
        options?: OptionsType | undefined;
        optionsMap?: {
            label?: string | undefined;
            value?: string | undefined;
        } | undefined;
        customGetStatus?: ((data: {
            options: OptionsRow<undefined>[];
            value: string | number;
            row: RecordType;
        }) => OptionsRow<undefined>) | undefined;
        tooltip?: string | Partial< ElTooltipProps> | ComputedRef<string> | ComputedRef<Partial< ElTooltipProps>> | undefined;
        render?: ((value: any, data: {
            row: RecordType;
            column: PlusColumn;
            index: number;
        }) => RenderTypes) | undefined;
        renderHTML?: ((value: any, data: {
            row: RecordType;
            column: PlusColumn;
            index: number;
        }) => string) | undefined;
        renderHeader?: ((label: string, props: PlusColumn) => RenderTypes) | undefined;
        renderDescriptionsItem?: ((data: {
            value: string;
            column: PlusColumn;
            row: RecordType;
        }) => RenderTypes) | undefined;
        renderDescriptionsLabel?: ((data: {
            label: string;
            column: PlusColumn;
            row: RecordType;
        }) => RenderTypes) | undefined;
        order?: number | ComputedRef<number> | undefined;
        children?: PlusColumn[] | undefined;
        headerFilter?: boolean | undefined;
        disabledHeaderFilter?: boolean | undefined;
        headerIsChecked?: boolean | undefined;
        tableColumnProps?: Partial<Omit< TableColumnCtx<any>, "label " | "prop" | "width" | "minWidth"> & {
            [key: string]: any;
        }> | undefined;
        preview?: boolean | undefined;
        linkText?: string | undefined;
        formatter?: ((value: any, data: {
            row: RecordType;
            column: PlusColumn;
            index: number;
        }) => string | number) | undefined;
        formProps?: Partial< PlusFormProps> | ComputedRef<Partial< PlusFormProps>> | ((value: FieldValueType, data: {
            row: Record<string, any>;
            index: number;
        }) => Partial< PlusFormProps>) | undefined;
        formItemProps?: PropsItemType< Mutable<FormItemProps> & {
            [key: string]: any;
            style?: CSSProperties | undefined;
        }> | undefined;
        fieldProps?: PropsItemType<Partial<{
            [key: string]: any;
            style: CSSProperties;
            rows: number;
            autocomplete: string;
            type: "" | "number" | "default" | "search" | "checkbox" | "radio" | "textarea" | "text" | "circle" | "color" | "button" | "success" | "warning" | "info" | "primary" | "danger" | "reset" | "submit" | "time" | "image" | "line" | "date" | "year" | "years" | "month" | "dates" | "week" | "datetime" | "datetimerange" | "daterange" | "monthrange" | "range" | "dashboard" | "hidden" | "datetime-local" | "email" | "file" | "password" | "tel" | "url";
            loading: EpPropMergeType<BooleanConstructor, unknown, unknown> | EpPropMergeType<StringConstructor, "lazy" | "eager", unknown> | undefined;
            step: string | number;
            format: string | ProgressFn | undefined;
            filterMethod: Function | ((node: CascaderNode, keyword: string) => boolean) | undefined;
            id: string | [string, string];
            effect: string;
            height: string | number;
            autosize: InputAutoSize;
        } & Mutable<Omit< AutocompleteProps, OmitTypes> & Omit< CascaderProps, OmitTypes> & Omit< CheckboxGroupProps, OmitTypes> & Omit< ColorPickerProps, OmitTypes> & Omit< DatePickerProps, OmitTypes> & Omit< InputProps, OmitTypes> & Omit< InputNumberProps, OmitTypes> & Omit< RadioGroupProps, OmitTypes> & Omit< RateProps, OmitTypes> & Omit< ISelectProps, OmitTypes> & Omit< SliderProps, OmitTypes> & Omit< SwitchProps, OmitTypes> & Omit< TimePickerDefaultProps, OmitTypes> & Omit< TimeSelectProps, OmitTypes> & Omit< PlusRadioProps, OmitTypes> & Omit< PlusDatePickerProps, OmitTypes> & Omit< PlusInputTagProps, OmitTypes> & Omit< TextProps, OmitTypes> & Omit< ImageProps, OmitTypes> & Omit< LinkProps, OmitTypes> & Omit< TagProps, OmitTypes> & Omit< ProgressProps, OmitTypes>>>> | undefined;
        renderField?: ((value: FieldValueType, onChange: (value: FieldValueType) => void, props: PlusColumn) => RenderTypes) | undefined;
        colProps?: Partial< Mutable<ColProps> & {
            [key: string]: any;
            style?: CSSProperties | undefined;
        }> | undefined;
        hasLabel?: boolean | Ref<boolean> | ComputedRef<boolean> | undefined;
        renderLabel?: ((label: string, props: PlusColumn) => RenderTypes) | undefined;
        renderExtra?: ((column: PlusColumn) => RenderTypes) | undefined;
        fieldSlots?: {
            [slotName: string]: (data?: any) => RenderTypes;
        } | undefined;
        fieldChildrenSlot?: ((option?: OptionsRow<undefined> | undefined) => RenderTypes) | undefined;
        renderErrorMessage?: ((props: PlusColumn & {
            value?: FieldValueType;
            error?: string | undefined;
            label?: string | undefined;
        }) => RenderTypes) | undefined;
    }) => any>> & Partial<Record<NonNullable<string | number>, (_: {
        [x: string]: any;
        label?: string | ComputedRef<string> | undefined;
        prop: string;
        width?: string | number | undefined;
        minWidth?: string | number | undefined;
        editable?: boolean | undefined;
        valueType?: TableValueType | FormItemValueType;
        hideInDescriptions?: boolean | Ref<boolean> | ComputedRef<boolean> | undefined;
        hideInForm?: boolean | Ref<boolean> | ComputedRef<boolean> | undefined;
        hideInTable?: boolean | Ref<boolean> | ComputedRef<boolean> | undefined;
        hideInSearch?: boolean | Ref<boolean> | ComputedRef<boolean> | undefined;
        descriptionsItemProps?: RecordType | undefined;
        options?: OptionsType | undefined;
        optionsMap?: {
            label?: string | undefined;
            value?: string | undefined;
        } | undefined;
        customGetStatus?: ((data: {
            options: OptionsRow<undefined>[];
            value: string | number;
            row: RecordType;
        }) => OptionsRow<undefined>) | undefined;
        tooltip?: string | Partial< ElTooltipProps> | ComputedRef<string> | ComputedRef<Partial< ElTooltipProps>> | undefined;
        render?: ((value: any, data: {
            row: RecordType;
            column: PlusColumn;
            index: number;
        }) => RenderTypes) | undefined;
        renderHTML?: ((value: any, data: {
            row: RecordType;
            column: PlusColumn;
            index: number;
        }) => string) | undefined;
        renderHeader?: ((label: string, props: PlusColumn) => RenderTypes) | undefined;
        renderDescriptionsItem?: ((data: {
            value: string;
            column: PlusColumn;
            row: RecordType;
        }) => RenderTypes) | undefined;
        renderDescriptionsLabel?: ((data: {
            label: string;
            column: PlusColumn;
            row: RecordType;
        }) => RenderTypes) | undefined;
        order?: number | ComputedRef<number> | undefined;
        children?: PlusColumn[] | undefined;
        headerFilter?: boolean | undefined;
        disabledHeaderFilter?: boolean | undefined;
        headerIsChecked?: boolean | undefined;
        tableColumnProps?: Partial<Omit< TableColumnCtx<any>, "label " | "prop" | "width" | "minWidth"> & {
            [key: string]: any;
        }> | undefined;
        preview?: boolean | undefined;
        linkText?: string | undefined;
        formatter?: ((value: any, data: {
            row: RecordType;
            column: PlusColumn;
            index: number;
        }) => string | number) | undefined;
        formProps?: Partial< PlusFormProps> | ComputedRef<Partial< PlusFormProps>> | ((value: FieldValueType, data: {
            row: Record<string, any>;
            index: number;
        }) => Partial< PlusFormProps>) | undefined;
        formItemProps?: PropsItemType< Mutable<FormItemProps> & {
            [key: string]: any;
            style?: CSSProperties | undefined;
        }> | undefined;
        fieldProps?: PropsItemType<Partial<{
            [key: string]: any;
            style: CSSProperties;
            rows: number;
            autocomplete: string;
            type: "" | "number" | "default" | "search" | "checkbox" | "radio" | "textarea" | "text" | "circle" | "color" | "button" | "success" | "warning" | "info" | "primary" | "danger" | "reset" | "submit" | "time" | "image" | "line" | "date" | "year" | "years" | "month" | "dates" | "week" | "datetime" | "datetimerange" | "daterange" | "monthrange" | "range" | "dashboard" | "hidden" | "datetime-local" | "email" | "file" | "password" | "tel" | "url";
            loading: EpPropMergeType<BooleanConstructor, unknown, unknown> | EpPropMergeType<StringConstructor, "lazy" | "eager", unknown> | undefined;
            step: string | number;
            format: string | ProgressFn | undefined;
            filterMethod: Function | ((node: CascaderNode, keyword: string) => boolean) | undefined;
            id: string | [string, string];
            effect: string;
            height: string | number;
            autosize: InputAutoSize;
        } & Mutable<Omit< AutocompleteProps, OmitTypes> & Omit< CascaderProps, OmitTypes> & Omit< CheckboxGroupProps, OmitTypes> & Omit< ColorPickerProps, OmitTypes> & Omit< DatePickerProps, OmitTypes> & Omit< InputProps, OmitTypes> & Omit< InputNumberProps, OmitTypes> & Omit< RadioGroupProps, OmitTypes> & Omit< RateProps, OmitTypes> & Omit< ISelectProps, OmitTypes> & Omit< SliderProps, OmitTypes> & Omit< SwitchProps, OmitTypes> & Omit< TimePickerDefaultProps, OmitTypes> & Omit< TimeSelectProps, OmitTypes> & Omit< PlusRadioProps, OmitTypes> & Omit< PlusDatePickerProps, OmitTypes> & Omit< PlusInputTagProps, OmitTypes> & Omit< TextProps, OmitTypes> & Omit< ImageProps, OmitTypes> & Omit< LinkProps, OmitTypes> & Omit< TagProps, OmitTypes> & Omit< ProgressProps, OmitTypes>>>> | undefined;
        renderField?: ((value: FieldValueType, onChange: (value: FieldValueType) => void, props: PlusColumn) => RenderTypes) | undefined;
        colProps?: Partial< Mutable<ColProps> & {
            [key: string]: any;
            style?: CSSProperties | undefined;
        }> | undefined;
        hasLabel?: boolean | Ref<boolean> | ComputedRef<boolean> | undefined;
        renderLabel?: ((label: string, props: PlusColumn) => RenderTypes) | undefined;
        renderExtra?: ((column: PlusColumn) => RenderTypes) | undefined;
        fieldSlots?: {
            [slotName: string]: (data?: any) => RenderTypes;
        } | undefined;
        fieldChildrenSlot?: ((option?: OptionsRow<undefined> | undefined) => RenderTypes) | undefined;
        renderErrorMessage?: ((props: PlusColumn & {
            value?: FieldValueType;
            error?: string | undefined;
            label?: string | undefined;
        }) => RenderTypes) | undefined;
    }) => any>> & Partial<Record<NonNullable<string | number>, (_: {
        [x: string]: any;
        label?: string | ComputedRef<string> | undefined;
        prop: string;
        width?: string | number | undefined;
        minWidth?: string | number | undefined;
        editable?: boolean | undefined;
        valueType?: TableValueType | FormItemValueType;
        hideInDescriptions?: boolean | Ref<boolean> | ComputedRef<boolean> | undefined;
        hideInForm?: boolean | Ref<boolean> | ComputedRef<boolean> | undefined;
        hideInTable?: boolean | Ref<boolean> | ComputedRef<boolean> | undefined;
        hideInSearch?: boolean | Ref<boolean> | ComputedRef<boolean> | undefined;
        descriptionsItemProps?: RecordType | undefined;
        options?: OptionsType | undefined;
        optionsMap?: {
            label?: string | undefined;
            value?: string | undefined;
        } | undefined;
        customGetStatus?: ((data: {
            options: OptionsRow<undefined>[];
            value: string | number;
            row: RecordType;
        }) => OptionsRow<undefined>) | undefined;
        tooltip?: string | Partial< ElTooltipProps> | ComputedRef<string> | ComputedRef<Partial< ElTooltipProps>> | undefined;
        render?: ((value: any, data: {
            row: RecordType;
            column: PlusColumn;
            index: number;
        }) => RenderTypes) | undefined;
        renderHTML?: ((value: any, data: {
            row: RecordType;
            column: PlusColumn;
            index: number;
        }) => string) | undefined;
        renderHeader?: ((label: string, props: PlusColumn) => RenderTypes) | undefined;
        renderDescriptionsItem?: ((data: {
            value: string;
            column: PlusColumn;
            row: RecordType;
        }) => RenderTypes) | undefined;
        renderDescriptionsLabel?: ((data: {
            label: string;
            column: PlusColumn;
            row: RecordType;
        }) => RenderTypes) | undefined;
        order?: number | ComputedRef<number> | undefined;
        children?: PlusColumn[] | undefined;
        headerFilter?: boolean | undefined;
        disabledHeaderFilter?: boolean | undefined;
        headerIsChecked?: boolean | undefined;
        tableColumnProps?: Partial<Omit< TableColumnCtx<any>, "label " | "prop" | "width" | "minWidth"> & {
            [key: string]: any;
        }> | undefined;
        preview?: boolean | undefined;
        linkText?: string | undefined;
        formatter?: ((value: any, data: {
            row: RecordType;
            column: PlusColumn;
            index: number;
        }) => string | number) | undefined;
        formProps?: Partial< PlusFormProps> | ComputedRef<Partial< PlusFormProps>> | ((value: FieldValueType, data: {
            row: Record<string, any>;
            index: number;
        }) => Partial< PlusFormProps>) | undefined;
        formItemProps?: PropsItemType< Mutable<FormItemProps> & {
            [key: string]: any;
            style?: CSSProperties | undefined;
        }> | undefined;
        fieldProps?: PropsItemType<Partial<{
            [key: string]: any;
            style: CSSProperties;
            rows: number;
            autocomplete: string;
            type: "" | "number" | "default" | "search" | "checkbox" | "radio" | "textarea" | "text" | "circle" | "color" | "button" | "success" | "warning" | "info" | "primary" | "danger" | "reset" | "submit" | "time" | "image" | "line" | "date" | "year" | "years" | "month" | "dates" | "week" | "datetime" | "datetimerange" | "daterange" | "monthrange" | "range" | "dashboard" | "hidden" | "datetime-local" | "email" | "file" | "password" | "tel" | "url";
            loading: EpPropMergeType<BooleanConstructor, unknown, unknown> | EpPropMergeType<StringConstructor, "lazy" | "eager", unknown> | undefined;
            step: string | number;
            format: string | ProgressFn | undefined;
            filterMethod: Function | ((node: CascaderNode, keyword: string) => boolean) | undefined;
            id: string | [string, string];
            effect: string;
            height: string | number;
            autosize: InputAutoSize;
        } & Mutable<Omit< AutocompleteProps, OmitTypes> & Omit< CascaderProps, OmitTypes> & Omit< CheckboxGroupProps, OmitTypes> & Omit< ColorPickerProps, OmitTypes> & Omit< DatePickerProps, OmitTypes> & Omit< InputProps, OmitTypes> & Omit< InputNumberProps, OmitTypes> & Omit< RadioGroupProps, OmitTypes> & Omit< RateProps, OmitTypes> & Omit< ISelectProps, OmitTypes> & Omit< SliderProps, OmitTypes> & Omit< SwitchProps, OmitTypes> & Omit< TimePickerDefaultProps, OmitTypes> & Omit< TimeSelectProps, OmitTypes> & Omit< PlusRadioProps, OmitTypes> & Omit< PlusDatePickerProps, OmitTypes> & Omit< PlusInputTagProps, OmitTypes> & Omit< TextProps, OmitTypes> & Omit< ImageProps, OmitTypes> & Omit< LinkProps, OmitTypes> & Omit< TagProps, OmitTypes> & Omit< ProgressProps, OmitTypes>>>> | undefined;
        renderField?: ((value: FieldValueType, onChange: (value: FieldValueType) => void, props: PlusColumn) => RenderTypes) | undefined;
        colProps?: Partial< Mutable<ColProps> & {
            [key: string]: any;
            style?: CSSProperties | undefined;
        }> | undefined;
        hasLabel?: boolean | Ref<boolean> | ComputedRef<boolean> | undefined;
        renderLabel?: ((label: string, props: PlusColumn) => RenderTypes) | undefined;
        renderExtra?: ((column: PlusColumn) => RenderTypes) | undefined;
        fieldSlots?: {
            [slotName: string]: (data?: any) => RenderTypes;
        } | undefined;
        fieldChildrenSlot?: ((option?: OptionsRow<undefined> | undefined) => RenderTypes) | undefined;
        renderErrorMessage?: ((props: PlusColumn & {
            value?: FieldValueType;
            error?: string | undefined;
            label?: string | undefined;
        }) => RenderTypes) | undefined;
    }) => any>> & Partial<Record<NonNullable<string | number>, (_: {
        [x: string]: any;
        label?: string | ComputedRef<string> | undefined;
        prop: string;
        width?: string | number | undefined;
        minWidth?: string | number | undefined;
        editable?: boolean | undefined;
        valueType?: TableValueType | FormItemValueType;
        hideInDescriptions?: boolean | Ref<boolean> | ComputedRef<boolean> | undefined;
        hideInForm?: boolean | Ref<boolean> | ComputedRef<boolean> | undefined;
        hideInTable?: boolean | Ref<boolean> | ComputedRef<boolean> | undefined;
        hideInSearch?: boolean | Ref<boolean> | ComputedRef<boolean> | undefined;
        descriptionsItemProps?: RecordType | undefined;
        options?: OptionsType | undefined;
        optionsMap?: {
            label?: string | undefined;
            value?: string | undefined;
        } | undefined;
        customGetStatus?: ((data: {
            options: OptionsRow<undefined>[];
            value: string | number;
            row: RecordType;
        }) => OptionsRow<undefined>) | undefined;
        tooltip?: string | Partial< ElTooltipProps> | ComputedRef<string> | ComputedRef<Partial< ElTooltipProps>> | undefined;
        render?: ((value: any, data: {
            row: RecordType;
            column: PlusColumn;
            index: number;
        }) => RenderTypes) | undefined;
        renderHTML?: ((value: any, data: {
            row: RecordType;
            column: PlusColumn;
            index: number;
        }) => string) | undefined;
        renderHeader?: ((label: string, props: PlusColumn) => RenderTypes) | undefined;
        renderDescriptionsItem?: ((data: {
            value: string;
            column: PlusColumn;
            row: RecordType;
        }) => RenderTypes) | undefined;
        renderDescriptionsLabel?: ((data: {
            label: string;
            column: PlusColumn;
            row: RecordType;
        }) => RenderTypes) | undefined;
        order?: number | ComputedRef<number> | undefined;
        children?: PlusColumn[] | undefined;
        headerFilter?: boolean | undefined;
        disabledHeaderFilter?: boolean | undefined;
        headerIsChecked?: boolean | undefined;
        tableColumnProps?: Partial<Omit< TableColumnCtx<any>, "label " | "prop" | "width" | "minWidth"> & {
            [key: string]: any;
        }> | undefined;
        preview?: boolean | undefined;
        linkText?: string | undefined;
        formatter?: ((value: any, data: {
            row: RecordType;
            column: PlusColumn;
            index: number;
        }) => string | number) | undefined;
        formProps?: Partial< PlusFormProps> | ComputedRef<Partial< PlusFormProps>> | ((value: FieldValueType, data: {
            row: Record<string, any>;
            index: number;
        }) => Partial< PlusFormProps>) | undefined;
        formItemProps?: PropsItemType< Mutable<FormItemProps> & {
            [key: string]: any;
            style?: CSSProperties | undefined;
        }> | undefined;
        fieldProps?: PropsItemType<Partial<{
            [key: string]: any;
            style: CSSProperties;
            rows: number;
            autocomplete: string;
            type: "" | "number" | "default" | "search" | "checkbox" | "radio" | "textarea" | "text" | "circle" | "color" | "button" | "success" | "warning" | "info" | "primary" | "danger" | "reset" | "submit" | "time" | "image" | "line" | "date" | "year" | "years" | "month" | "dates" | "week" | "datetime" | "datetimerange" | "daterange" | "monthrange" | "range" | "dashboard" | "hidden" | "datetime-local" | "email" | "file" | "password" | "tel" | "url";
            loading: EpPropMergeType<BooleanConstructor, unknown, unknown> | EpPropMergeType<StringConstructor, "lazy" | "eager", unknown> | undefined;
            step: string | number;
            format: string | ProgressFn | undefined;
            filterMethod: Function | ((node: CascaderNode, keyword: string) => boolean) | undefined;
            id: string | [string, string];
            effect: string;
            height: string | number;
            autosize: InputAutoSize;
        } & Mutable<Omit< AutocompleteProps, OmitTypes> & Omit< CascaderProps, OmitTypes> & Omit< CheckboxGroupProps, OmitTypes> & Omit< ColorPickerProps, OmitTypes> & Omit< DatePickerProps, OmitTypes> & Omit< InputProps, OmitTypes> & Omit< InputNumberProps, OmitTypes> & Omit< RadioGroupProps, OmitTypes> & Omit< RateProps, OmitTypes> & Omit< ISelectProps, OmitTypes> & Omit< SliderProps, OmitTypes> & Omit< SwitchProps, OmitTypes> & Omit< TimePickerDefaultProps, OmitTypes> & Omit< TimeSelectProps, OmitTypes> & Omit< PlusRadioProps, OmitTypes> & Omit< PlusDatePickerProps, OmitTypes> & Omit< PlusInputTagProps, OmitTypes> & Omit< TextProps, OmitTypes> & Omit< ImageProps, OmitTypes> & Omit< LinkProps, OmitTypes> & Omit< TagProps, OmitTypes> & Omit< ProgressProps, OmitTypes>>>> | undefined;
        renderField?: ((value: FieldValueType, onChange: (value: FieldValueType) => void, props: PlusColumn) => RenderTypes) | undefined;
        colProps?: Partial< Mutable<ColProps> & {
            [key: string]: any;
            style?: CSSProperties | undefined;
        }> | undefined;
        hasLabel?: boolean | Ref<boolean> | ComputedRef<boolean> | undefined;
        renderLabel?: ((label: string, props: PlusColumn) => RenderTypes) | undefined;
        renderExtra?: ((column: PlusColumn) => RenderTypes) | undefined;
        fieldSlots?: {
            [slotName: string]: (data?: any) => RenderTypes;
        } | undefined;
        fieldChildrenSlot?: ((option?: OptionsRow<undefined> | undefined) => RenderTypes) | undefined;
        renderErrorMessage?: ((props: PlusColumn & {
            value?: FieldValueType;
            error?: string | undefined;
            label?: string | undefined;
        }) => RenderTypes) | undefined;
    }) => any>> & Partial<Record<NonNullable<string | number>, (_: {
        [x: string]: any;
        label?: string | ComputedRef<string> | undefined;
        prop: string;
        width?: string | number | undefined;
        minWidth?: string | number | undefined;
        editable?: boolean | undefined;
        valueType?: TableValueType | FormItemValueType;
        hideInDescriptions?: boolean | Ref<boolean> | ComputedRef<boolean> | undefined;
        hideInForm?: boolean | Ref<boolean> | ComputedRef<boolean> | undefined;
        hideInTable?: boolean | Ref<boolean> | ComputedRef<boolean> | undefined;
        hideInSearch?: boolean | Ref<boolean> | ComputedRef<boolean> | undefined;
        descriptionsItemProps?: RecordType | undefined;
        options?: OptionsType | undefined;
        optionsMap?: {
            label?: string | undefined;
            value?: string | undefined;
        } | undefined;
        customGetStatus?: ((data: {
            options: OptionsRow<undefined>[];
            value: string | number;
            row: RecordType;
        }) => OptionsRow<undefined>) | undefined;
        tooltip?: string | Partial< ElTooltipProps> | ComputedRef<string> | ComputedRef<Partial< ElTooltipProps>> | undefined;
        render?: ((value: any, data: {
            row: RecordType;
            column: PlusColumn;
            index: number;
        }) => RenderTypes) | undefined;
        renderHTML?: ((value: any, data: {
            row: RecordType;
            column: PlusColumn;
            index: number;
        }) => string) | undefined;
        renderHeader?: ((label: string, props: PlusColumn) => RenderTypes) | undefined;
        renderDescriptionsItem?: ((data: {
            value: string;
            column: PlusColumn;
            row: RecordType;
        }) => RenderTypes) | undefined;
        renderDescriptionsLabel?: ((data: {
            label: string;
            column: PlusColumn;
            row: RecordType;
        }) => RenderTypes) | undefined;
        order?: number | ComputedRef<number> | undefined;
        children?: PlusColumn[] | undefined;
        headerFilter?: boolean | undefined;
        disabledHeaderFilter?: boolean | undefined;
        headerIsChecked?: boolean | undefined;
        tableColumnProps?: Partial<Omit< TableColumnCtx<any>, "label " | "prop" | "width" | "minWidth"> & {
            [key: string]: any;
        }> | undefined;
        preview?: boolean | undefined;
        linkText?: string | undefined;
        formatter?: ((value: any, data: {
            row: RecordType;
            column: PlusColumn;
            index: number;
        }) => string | number) | undefined;
        formProps?: Partial< PlusFormProps> | ComputedRef<Partial< PlusFormProps>> | ((value: FieldValueType, data: {
            row: Record<string, any>;
            index: number;
        }) => Partial< PlusFormProps>) | undefined;
        formItemProps?: PropsItemType< Mutable<FormItemProps> & {
            [key: string]: any;
            style?: CSSProperties | undefined;
        }> | undefined;
        fieldProps?: PropsItemType<Partial<{
            [key: string]: any;
            style: CSSProperties;
            rows: number;
            autocomplete: string;
            type: "" | "number" | "default" | "search" | "checkbox" | "radio" | "textarea" | "text" | "circle" | "color" | "button" | "success" | "warning" | "info" | "primary" | "danger" | "reset" | "submit" | "time" | "image" | "line" | "date" | "year" | "years" | "month" | "dates" | "week" | "datetime" | "datetimerange" | "daterange" | "monthrange" | "range" | "dashboard" | "hidden" | "datetime-local" | "email" | "file" | "password" | "tel" | "url";
            loading: EpPropMergeType<BooleanConstructor, unknown, unknown> | EpPropMergeType<StringConstructor, "lazy" | "eager", unknown> | undefined;
            step: string | number;
            format: string | ProgressFn | undefined;
            filterMethod: Function | ((node: CascaderNode, keyword: string) => boolean) | undefined;
            id: string | [string, string];
            effect: string;
            height: string | number;
            autosize: InputAutoSize;
        } & Mutable<Omit< AutocompleteProps, OmitTypes> & Omit< CascaderProps, OmitTypes> & Omit< CheckboxGroupProps, OmitTypes> & Omit< ColorPickerProps, OmitTypes> & Omit< DatePickerProps, OmitTypes> & Omit< InputProps, OmitTypes> & Omit< InputNumberProps, OmitTypes> & Omit< RadioGroupProps, OmitTypes> & Omit< RateProps, OmitTypes> & Omit< ISelectProps, OmitTypes> & Omit< SliderProps, OmitTypes> & Omit< SwitchProps, OmitTypes> & Omit< TimePickerDefaultProps, OmitTypes> & Omit< TimeSelectProps, OmitTypes> & Omit< PlusRadioProps, OmitTypes> & Omit< PlusDatePickerProps, OmitTypes> & Omit< PlusInputTagProps, OmitTypes> & Omit< TextProps, OmitTypes> & Omit< ImageProps, OmitTypes> & Omit< LinkProps, OmitTypes> & Omit< TagProps, OmitTypes> & Omit< ProgressProps, OmitTypes>>>> | undefined;
        renderField?: ((value: FieldValueType, onChange: (value: FieldValueType) => void, props: PlusColumn) => RenderTypes) | undefined;
        colProps?: Partial< Mutable<ColProps> & {
            [key: string]: any;
            style?: CSSProperties | undefined;
        }> | undefined;
        hasLabel?: boolean | Ref<boolean> | ComputedRef<boolean> | undefined;
        renderLabel?: ((label: string, props: PlusColumn) => RenderTypes) | undefined;
        renderExtra?: ((column: PlusColumn) => RenderTypes) | undefined;
        fieldSlots?: {
            [slotName: string]: (data?: any) => RenderTypes;
        } | undefined;
        fieldChildrenSlot?: ((option?: OptionsRow<undefined> | undefined) => RenderTypes) | undefined;
        renderErrorMessage?: ((props: PlusColumn & {
            value?: FieldValueType;
            error?: string | undefined;
            label?: string | undefined;
        }) => RenderTypes) | undefined;
    }) => any>> & {
        default?(_: {}): any;
        "group-header"?(_: {
            title: string;
            columns: PlusColumn[];
            icon: Component | undefined;
            index: number;
        }): any;
        "tooltip-icon"?(_: {}): any;
        "search-footer"?(_: {}): any;
        footer?(_: {
            handleReset: () => void;
            handleSubmit: () => Promise<boolean>;
        }): any;
    };
});
