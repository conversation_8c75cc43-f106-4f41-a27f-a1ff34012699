"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var _exportNames={},_formItem=(exports.default=void 0,_interopRequireWildcard(require("../form-item")));function _getRequireWildcardCache(e){var t,r;return"function"!=typeof WeakMap?null:(t=new WeakMap,r=new WeakMap,(_getRequireWildcardCache=function(e){return e?r:t})(e))}function _interopRequireWildcard(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};t=_getRequireWildcardCache(t);if(t&&t.has(e))return t.get(e);var r,o,n={__proto__:null},u=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(r in e)"default"!==r&&{}.hasOwnProperty.call(e,r)&&((o=u?Object.getOwnPropertyDescriptor(e,r):null)&&(o.get||o.set)?Object.defineProperty(n,r,o):n[r]=e[r]);return n.default=e,t&&t.set(e,n),n}Object.keys(_formItem).forEach(function(e){"default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(_exportNames,e)||e in exports&&exports[e]===_formItem[e]||Object.defineProperty(exports,e,{enumerable:!0,get:function(){return _formItem[e]}})});var _default=exports.default=_formItem.default;