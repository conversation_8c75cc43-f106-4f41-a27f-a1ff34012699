{"version": 3, "file": "matching.js", "sources": ["../../../src/matcher/spatial/matching.ts"], "sourcesContent": ["import { sorted, extend } from '../../helper';\nimport { zxcvbnOptions } from '../../Options';\n/*\n * ------------------------------------------------------------------------------\n * spatial match (qwerty/dvorak/keypad and so on) -----------------------------------------\n * ------------------------------------------------------------------------------\n */\nclass MatchSpatial {\n    constructor() {\n        this.SHIFTED_RX = /[~!@#$%^&*()_+QWERTYUIOP{}|ASDFGHJKL:\"ZXCVBNM<>?]/;\n    }\n    match({ password }) {\n        const matches = [];\n        Object.keys(zxcvbnOptions.graphs).forEach((graphName) => {\n            const graph = zxcvbnOptions.graphs[graphName];\n            extend(matches, this.helper(password, graph, graphName));\n        });\n        return sorted(matches);\n    }\n    checkIfShifted(graphName, password, index) {\n        if (!graphName.includes('keypad') &&\n            // initial character is shifted\n            this.SHIFTED_RX.test(password.charAt(index))) {\n            return 1;\n        }\n        return 0;\n    }\n    // eslint-disable-next-line complexity, max-statements\n    helper(password, graph, graphName) {\n        let shiftedCount;\n        const matches = [];\n        let i = 0;\n        const passwordLength = password.length;\n        while (i < passwordLength - 1) {\n            let j = i + 1;\n            let lastDirection = null;\n            let turns = 0;\n            shiftedCount = this.checkIfShifted(graphName, password, i);\n            // eslint-disable-next-line no-constant-condition\n            while (true) {\n                const prevChar = password.charAt(j - 1);\n                const adjacents = graph[prevChar] || [];\n                let found = false;\n                let foundDirection = -1;\n                let curDirection = -1;\n                // consider growing pattern by one character if j hasn't gone over the edge.\n                if (j < passwordLength) {\n                    const curChar = password.charAt(j);\n                    const adjacentsLength = adjacents.length;\n                    for (let k = 0; k < adjacentsLength; k += 1) {\n                        const adjacent = adjacents[k];\n                        curDirection += 1;\n                        // eslint-disable-next-line max-depth\n                        if (adjacent) {\n                            const adjacentIndex = adjacent.indexOf(curChar);\n                            // eslint-disable-next-line max-depth\n                            if (adjacentIndex !== -1) {\n                                found = true;\n                                foundDirection = curDirection;\n                                // eslint-disable-next-line max-depth\n                                if (adjacentIndex === 1) {\n                                    // # index 1 in the adjacency means the key is shifted,\n                                    // # 0 means unshifted: A vs a, % vs 5, etc.\n                                    // # for example, 'q' is adjacent to the entry '2@'.\n                                    // # @ is shifted w/ index 1, 2 is unshifted.\n                                    shiftedCount += 1;\n                                }\n                                // eslint-disable-next-line max-depth\n                                if (lastDirection !== foundDirection) {\n                                    // # adding a turn is correct even in the initial\n                                    // case when last_direction is null:\n                                    // # every spatial pattern starts with a turn.\n                                    turns += 1;\n                                    lastDirection = foundDirection;\n                                }\n                                break;\n                            }\n                        }\n                    }\n                }\n                // if the current pattern continued, extend j and try to grow again\n                if (found) {\n                    j += 1;\n                    // otherwise push the pattern discovered so far, if any...\n                }\n                else {\n                    // don't consider length 1 or 2 chains.\n                    if (j - i > 2) {\n                        matches.push({\n                            pattern: 'spatial',\n                            i,\n                            j: j - 1,\n                            token: password.slice(i, j),\n                            graph: graphName,\n                            turns,\n                            shiftedCount,\n                        });\n                    }\n                    // ...and then start a new search for the rest of the password.\n                    i = j;\n                    break;\n                }\n            }\n        }\n        return matches;\n    }\n}\nexport default MatchSpatial;\n//# sourceMappingURL=matching.js.map"], "names": ["MatchSpatial", "constructor", "SHIFTED_RX", "match", "password", "matches", "Object", "keys", "zxcvbnOptions", "graphs", "for<PERSON>ach", "graphName", "graph", "extend", "helper", "sorted", "checkIfShifted", "index", "includes", "test", "char<PERSON>t", "shiftedCount", "i", "<PERSON><PERSON><PERSON><PERSON>", "length", "j", "lastDirection", "turns", "prevChar", "adjacents", "found", "foundDirection", "curDirection", "cur<PERSON><PERSON>", "adjacentsLength", "k", "adjacent", "adjacentIndex", "indexOf", "push", "pattern", "token", "slice"], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMA,YAAY,CAAC;AACfC,EAAAA,WAAWA,GAAG;IACV,IAAI,CAACC,UAAU,GAAG,mDAAmD,CAAA;AACzE,GAAA;AACAC,EAAAA,KAAKA,CAAC;AAAEC,IAAAA,QAAAA;AAAS,GAAC,EAAE;IAChB,MAAMC,OAAO,GAAG,EAAE,CAAA;IAClBC,MAAM,CAACC,IAAI,CAACC,qBAAa,CAACC,MAAM,CAAC,CAACC,OAAO,CAAEC,SAAS,IAAK;AACrD,MAAA,MAAMC,KAAK,GAAGJ,qBAAa,CAACC,MAAM,CAACE,SAAS,CAAC,CAAA;AAC7CE,MAAAA,aAAM,CAACR,OAAO,EAAE,IAAI,CAACS,MAAM,CAACV,QAAQ,EAAEQ,KAAK,EAAED,SAAS,CAAC,CAAC,CAAA;AAC5D,KAAC,CAAC,CAAA;IACF,OAAOI,aAAM,CAACV,OAAO,CAAC,CAAA;AAC1B,GAAA;AACAW,EAAAA,cAAcA,CAACL,SAAS,EAAEP,QAAQ,EAAEa,KAAK,EAAE;AACvC,IAAA,IAAI,CAACN,SAAS,CAACO,QAAQ,CAAC,QAAQ,CAAC;AAC7B;AACA,IAAA,IAAI,CAAChB,UAAU,CAACiB,IAAI,CAACf,QAAQ,CAACgB,MAAM,CAACH,KAAK,CAAC,CAAC,EAAE;AAC9C,MAAA,OAAO,CAAC,CAAA;AACZ,KAAA;AACA,IAAA,OAAO,CAAC,CAAA;AACZ,GAAA;AACA;AACAH,EAAAA,MAAMA,CAACV,QAAQ,EAAEQ,KAAK,EAAED,SAAS,EAAE;AAC/B,IAAA,IAAIU,YAAY,CAAA;IAChB,MAAMhB,OAAO,GAAG,EAAE,CAAA;IAClB,IAAIiB,CAAC,GAAG,CAAC,CAAA;AACT,IAAA,MAAMC,cAAc,GAAGnB,QAAQ,CAACoB,MAAM,CAAA;AACtC,IAAA,OAAOF,CAAC,GAAGC,cAAc,GAAG,CAAC,EAAE;AAC3B,MAAA,IAAIE,CAAC,GAAGH,CAAC,GAAG,CAAC,CAAA;MACb,IAAII,aAAa,GAAG,IAAI,CAAA;MACxB,IAAIC,KAAK,GAAG,CAAC,CAAA;MACbN,YAAY,GAAG,IAAI,CAACL,cAAc,CAACL,SAAS,EAAEP,QAAQ,EAAEkB,CAAC,CAAC,CAAA;AAC1D;AACA,MAAA,OAAO,IAAI,EAAE;QACT,MAAMM,QAAQ,GAAGxB,QAAQ,CAACgB,MAAM,CAACK,CAAC,GAAG,CAAC,CAAC,CAAA;AACvC,QAAA,MAAMI,SAAS,GAAGjB,KAAK,CAACgB,QAAQ,CAAC,IAAI,EAAE,CAAA;QACvC,IAAIE,KAAK,GAAG,KAAK,CAAA;QACjB,IAAIC,cAAc,GAAG,CAAC,CAAC,CAAA;QACvB,IAAIC,YAAY,GAAG,CAAC,CAAC,CAAA;AACrB;QACA,IAAIP,CAAC,GAAGF,cAAc,EAAE;AACpB,UAAA,MAAMU,OAAO,GAAG7B,QAAQ,CAACgB,MAAM,CAACK,CAAC,CAAC,CAAA;AAClC,UAAA,MAAMS,eAAe,GAAGL,SAAS,CAACL,MAAM,CAAA;AACxC,UAAA,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,eAAe,EAAEC,CAAC,IAAI,CAAC,EAAE;AACzC,YAAA,MAAMC,QAAQ,GAAGP,SAAS,CAACM,CAAC,CAAC,CAAA;AAC7BH,YAAAA,YAAY,IAAI,CAAC,CAAA;AACjB;AACA,YAAA,IAAII,QAAQ,EAAE;AACV,cAAA,MAAMC,aAAa,GAAGD,QAAQ,CAACE,OAAO,CAACL,OAAO,CAAC,CAAA;AAC/C;AACA,cAAA,IAAII,aAAa,KAAK,CAAC,CAAC,EAAE;AACtBP,gBAAAA,KAAK,GAAG,IAAI,CAAA;AACZC,gBAAAA,cAAc,GAAGC,YAAY,CAAA;AAC7B;gBACA,IAAIK,aAAa,KAAK,CAAC,EAAE;AACrB;AACA;AACA;AACA;AACAhB,kBAAAA,YAAY,IAAI,CAAC,CAAA;AACrB,iBAAA;AACA;gBACA,IAAIK,aAAa,KAAKK,cAAc,EAAE;AAClC;AACA;AACA;AACAJ,kBAAAA,KAAK,IAAI,CAAC,CAAA;AACVD,kBAAAA,aAAa,GAAGK,cAAc,CAAA;AAClC,iBAAA;AACA,gBAAA,MAAA;AACJ,eAAA;AACJ,aAAA;AACJ,WAAA;AACJ,SAAA;AACA;AACA,QAAA,IAAID,KAAK,EAAE;AACPL,UAAAA,CAAC,IAAI,CAAC,CAAA;AACN;AACJ,SAAC,MACI;AACD;AACA,UAAA,IAAIA,CAAC,GAAGH,CAAC,GAAG,CAAC,EAAE;YACXjB,OAAO,CAACkC,IAAI,CAAC;AACTC,cAAAA,OAAO,EAAE,SAAS;cAClBlB,CAAC;cACDG,CAAC,EAAEA,CAAC,GAAG,CAAC;cACRgB,KAAK,EAAErC,QAAQ,CAACsC,KAAK,CAACpB,CAAC,EAAEG,CAAC,CAAC;AAC3Bb,cAAAA,KAAK,EAAED,SAAS;cAChBgB,KAAK;AACLN,cAAAA,YAAAA;AACJ,aAAC,CAAC,CAAA;AACN,WAAA;AACA;AACAC,UAAAA,CAAC,GAAGG,CAAC,CAAA;AACL,UAAA,MAAA;AACJ,SAAA;AACJ,OAAA;AACJ,KAAA;AACA,IAAA,OAAOpB,OAAO,CAAA;AAClB,GAAA;AACJ;;;;"}