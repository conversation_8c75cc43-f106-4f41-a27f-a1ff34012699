import { RouteComponent, RouteRecordRedirectOption, RouteLocationNormalized, RouteRecordName, NavigationGuardWithThis, RouteMeta } from 'vue-router';
import { SetupContext, Component, VNode, RendererNode, RendererElement } from 'vue';
import { FieldValueType, RecordType, PlusColumn, PlusRouteRecordRaw } from 'plus-pro-components/es/types';

export * from './format';
export * from './is';
/**
 * 获取table key
 * @param item
 * @returns
 */
export declare const getTableKey: (item: PlusColumn, hasEditable?: boolean) => string;
/**
 *  获取tooltip
 * @param tooltip
 * @returns
 */
export declare const getTooltip: (tooltip: PlusColumn['tooltip']) => RecordType;
/**
 * 获取自定义的props 支持对象 函数 和 Promise
 * @param props
 * @param value
 * @param row
 * @returns
 */
export declare const getCustomProps: (props: RecordType | ((...arg: any) => RecordType | Promise<RecordType>) | undefined, value: FieldValueType | undefined, row: RecordType, index: number, type: 'formItemProps' | 'fieldProps' | 'formProps') => Promise<any>;
/**
 * 处理slots
 */
export declare const handleSlots: (slots: PlusColumn['slots'], value: FieldValueType | undefined, params: RecordType) => RecordType;
/**
 * 处理slot名称
 */
export declare const getSlotName: (type: string, prop?: string | number) => string;
/**
 * 处理form-item中的slot名称
 */
export declare const getFieldSlotName: (prop?: string | number) => string;
/**
 *   处理form-item中的label slot名称
 */
export declare const getLabelSlotName: (prop?: string | number) => string;
/**
 *   处理form-item中的 extra slot名称
 */
export declare const getExtraSlotName: (prop?: string | number) => string;
/**
 * 处理table中的 header slot名称
 */
export declare const getTableHeaderSlotName: (prop?: string | number) => string;
/**
 * 处理table中的 cell slot名称
 */
export declare const getTableCellSlotName: (prop?: string | number) => string;
/**
 * 处理el-descriptions-item 的  slot名称
 */
export declare const getDescSlotName: (prop?: string | number) => string;
/**
 * 处理el-descriptions-item  label的  slot名称
 */
export declare const getDescLabelSlotName: (prop?: string | number) => string;
/**
 * 过滤slots
 * @param slots
 * @param name
 * @returns
 */
export declare const filterSlots: (slots: RecordType, name: string) => SetupContext['slots'];
/**
 *  获取值  支持 x.y.z
 * @param target
 * @param key
 * @returns
 */
export declare const getValue: (target: RecordType, key: string) => any;
/**
 *  设置值  支持 x.y.z
 * @param target
 * @param key
 * @returns
 */
export declare const setValue: (target: RecordType, key: string, value: any) => RecordType;
/**
 * 版本比较
 * @param version1
 * @param version2
 * @returns
 */
export declare const compareVersion: (version1: string, version2: string) => 1 | -1 | 0;
/**
 * element-plus版本号是否小于'2.6.0'
 */
export declare const versionIsLessThan260: boolean;
/**
 * element-plus版本号是否小于'2.9.9'
 */
export declare const versionIsLessThan299: boolean;
/**
 * 获取label
 * @param label
 * @returns
 */
export declare const getLabel: (label: PlusColumn['label']) => string;
/**
 * 移除children字段
 * @param item *
 * @version 0.1.21
 * @fixed https://github.com/plus-pro-components/plus-pro-components/issues/258
 */
export declare const removeChildrenField: (item: PlusRouteRecordRaw) => {
    __children: PlusRouteRecordRaw[] | undefined;
    end?: boolean | undefined;
    sensitive?: boolean | undefined;
    strict?: boolean | undefined;
    component?: ( RouteComponent | (() => Promise< RouteComponent>)) | null | undefined;
    components?: Record<string, RouteComponent | (() => Promise< RouteComponent>)> | null | undefined;
    redirect?: RouteRecordRedirectOption | undefined;
    props?: (boolean | Record<string, any> | ((to: RouteLocationNormalized) => Record<string, any>)) | Record<string, boolean | Record<string, any> | ((to: RouteLocationNormalized) => Record<string, any>)> | undefined;
    path?: string | undefined;
    alias?: string | string[] | undefined;
    name?: RouteRecordName | undefined;
    beforeEnter?: NavigationGuardWithThis<undefined> | NavigationGuardWithThis<undefined>[] | undefined;
    meta?: ( RouteMeta & {
        title?: string | undefined;
        icon?: Component | VNode<RendererNode, RendererElement, {
            [key: string]: any;
        }> | ((route: PlusRouteRecordRaw) => VNode<RendererNode, RendererElement, {
            [key: string]: any;
        }>) | undefined;
        sort?: number | undefined;
        hideInMenu?: boolean | undefined;
        hideInBreadcrumb?: boolean | undefined;
        disabled?: boolean | undefined;
    }) | undefined;
};
