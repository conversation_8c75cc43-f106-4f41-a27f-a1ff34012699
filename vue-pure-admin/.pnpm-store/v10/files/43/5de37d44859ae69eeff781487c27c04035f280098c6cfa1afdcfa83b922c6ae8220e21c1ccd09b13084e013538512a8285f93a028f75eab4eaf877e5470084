import { MessageElementsStyles, MessageRoleStyles, MessageStyles, UserContent } from '../../../types/messages';
import { MessageContentI, MessageToElements, Overwrite } from '../../../types/messagesInternal';
import { ProcessedTextToSpeechConfig } from './textToSpeech/textToSpeech';
import { BrowserStorage } from './browserStorage/browserStorage';
import { RemarkableOptions } from '../../../types/remarkable';
import { HTMLClassUtilities } from '../../../types/html';
import { IntroPanel } from '../introPanel/introPanel';
import { FocusMode } from '../../../types/focusMode';
import { Response } from '../../../types/response';
import { DeepChat } from '../../../deepChat';
import { MessageElements } from './messages';
import { Avatar } from './avatar';
import { Name } from './name';
export declare class MessagesBase {
    messageElementRefs: MessageElements[];
    textToSpeech?: ProcessedTextToSpeechConfig;
    submitUserMessage?: (content: UserContent) => void;
    readonly elementRef: HTMLElement;
    readonly focusMode?: FocusMode;
    readonly messageStyles?: MessageStyles;
    readonly htmlClassUtilities: HTMLClassUtilities;
    readonly messageToElements: MessageToElements;
    readonly avatar?: Avatar;
    readonly name?: Name;
    protected _introPanel?: IntroPanel;
    private _remarkable;
    private _lastGroupMessagesElement?;
    private readonly _onMessage?;
    readonly browserStorage?: BrowserStorage;
    static readonly TEXT_BUBBLE_CLASS = "text-message";
    static readonly INTRO_CLASS = "deep-chat-intro";
    static readonly LAST_GROUP_MESSAGES_ACTIVE = "deep-chat-last-group-messages-active";
    constructor(deepChat: DeepChat);
    private static createContainerElement;
    addNewTextMessage(text: string, role: string, overwrite?: Overwrite, isTop?: boolean): MessageElements;
    private overwriteText;
    protected createAndAppendNewMessageElement(text: string, role: string): MessageElements;
    private appendNewMessageElementFocusMode;
    private createNewGroupElementFocusMode;
    private createAndAppendNewMessageElementDefault;
    appendOuterContainerElemet(outerContainer: HTMLElement, role?: string): void;
    private createAndPrependNewMessageElement;
    createMessageElementsOnOrientation(text: string, role: string, isTop: boolean, loading?: boolean): MessageElements;
    createNewMessageElement(text: string, role: string, isTop?: boolean, loading?: boolean): MessageElements;
    private revealRoleElementsIfTempRemoved;
    protected static isTemporaryElement(elements: MessageElements): boolean;
    createElements(text: string, role: string): MessageElements;
    createMessageElements(text: string, role: string, isTop?: boolean): MessageElements;
    protected static createBaseElements(role: string): MessageElements;
    private addInnerContainerElements;
    applyCustomStyles(elements: MessageElements | undefined, role: string, media: boolean, otherStyles?: MessageRoleStyles | MessageElementsStyles): void;
    static createMessageContent(content: Response): MessageContentI;
    removeMessage(messageElements: MessageElements): void;
    removeLastMessage(): void;
    isLastMessageError(): boolean | undefined;
    static isLoadingMessage(elements?: MessageElements): boolean | undefined;
    sendClientUpdate(message: MessageContentI, isHistory?: boolean): void;
    renderText(bubbleElement: HTMLElement, text: string): void;
    protected refreshTextMessages(customConfig?: RemarkableOptions): void;
}
//# sourceMappingURL=messagesBase.d.ts.map