{"name": "rollup-plugin-external-globals", "version": "0.10.0", "description": "Transform external imports into global variables like output.globals.", "keywords": ["rollup-plugin", "es", "transform", "external", "globals"], "main": "index.js", "typings": "index.d.ts", "exports": {".": {"types": "./index.d.ts", "default": "./index.js"}}, "files": ["lib", "*.d.ts"], "eslintIgnore": ["coverage"], "scripts": {"test": "eslint **/*.js --cache && c8 --reporter lcov mocha", "preversion": "npm test", "postversion": "git push --follow-tags && npm publish"}, "repository": "eight04/rollup-plugin-external-globals", "author": "eight04 <<EMAIL>>", "license": "MIT", "devDependencies": {"c8": "^9.1.0", "endent": "^2.1.0", "eslint": "^8.56.0", "mocha": "^10.2.0", "rollup": "^4.9.6", "tempdir-yaml": "^0.3.0"}, "dependencies": {"@rollup/pluginutils": "^5.1.0", "estree-walker": "^3.0.3", "is-reference": "^3.0.2", "magic-string": "^0.30.5"}, "peerDependencies": {"rollup": "^2.25.0 || ^3.3.0 || ^4.1.4"}}