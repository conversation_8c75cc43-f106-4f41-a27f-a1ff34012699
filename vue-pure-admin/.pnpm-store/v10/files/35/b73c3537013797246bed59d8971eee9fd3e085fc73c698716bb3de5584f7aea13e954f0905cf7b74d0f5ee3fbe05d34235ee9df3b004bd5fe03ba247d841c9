'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
var elementPlus = require('element-plus');
var index = require('../../utils/index.js');
var is = require('../../utils/is.js');

var _sfc_main = /* @__PURE__ */ vue.defineComponent({
  ...{
    name: "PlusRadio"
  },
  __name: "index",
  props: {
    modelValue: { type: [String, Number, Boolean], default: "" },
    options: { default: () => [] },
    isCancel: { type: Boolean, default: true },
    fieldSlots: { default: void 0 },
    fieldChildrenSlot: { default: void 0 }
  },
  emits: ["change", "update:modelValue"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const radioInstance = vue.ref();
    const radioGroupInstance = vue.ref();
    const state = vue.reactive({ radio: "" });
    vue.watch(
      () => props.modelValue,
      (val) => {
        state.radio = val;
      },
      { immediate: true }
    );
    const attrs = vue.useAttrs();
    const radioClick = (e, val, fieldItemProps) => {
      if (Reflect.get(attrs, "disabled") || (fieldItemProps == null ? void 0 : fieldItemProps.disabled)) {
        return;
      }
      if (!props.isCancel) {
        return;
      } else {
        e.preventDefault();
      }
      state.radio = state.radio === val ? "" : val;
      emit("update:modelValue", state.radio);
      emit("change", state.radio);
    };
    const change = (val) => {
      if (props.isCancel) return;
      emit("update:modelValue", val);
      emit("change", val);
    };
    __expose({
      radioInstance,
      radioGroupInstance
    });
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createBlock(vue.unref(elementPlus.ElRadioGroup), vue.mergeProps({
        ref_key: "radioGroupInstance",
        ref: radioGroupInstance,
        modelValue: state.radio,
        "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => state.radio = $event),
        class: "plus-radio"
      }, _ctx.$attrs), vue.createSlots({
        default: vue.withCtx(() => [
          vue.unref(index.versionIsLessThan260) ? (vue.openBlock(true), vue.createElementBlock(
            vue.Fragment,
            { key: 0 },
            vue.renderList(_ctx.options, (item) => {
              return vue.openBlock(), vue.createBlock(vue.unref(elementPlus.ElRadio), vue.mergeProps({
                key: `${item.label}${item.value}`,
                ref_for: true,
                ref_key: "radioInstance",
                ref: radioInstance,
                label: item.value
              }, vue.unref(is.isFunction)(item.fieldItemProps) ? item.fieldItemProps(item) : item.fieldItemProps, {
                onClick: ($event) => radioClick(
                  $event,
                  item.value,
                  vue.unref(is.isFunction)(item.fieldItemProps) ? item.fieldItemProps(item) : item.fieldItemProps
                ),
                onChange: ($event) => change(item.value)
              }), {
                default: vue.withCtx(() => [
                  vue.unref(is.isFunction)(item.fieldSlot) ? (vue.openBlock(), vue.createBlock(vue.resolveDynamicComponent(item.fieldSlot), vue.mergeProps({
                    key: 0,
                    "model-value": state.radio,
                    column: props
                  }, item), null, 16, ["model-value"])) : vue.unref(is.isFunction)(_ctx.fieldChildrenSlot) ? (vue.openBlock(), vue.createBlock(vue.resolveDynamicComponent(_ctx.fieldChildrenSlot), vue.mergeProps({
                    key: 1,
                    "model-value": state.radio,
                    column: props
                  }, item), null, 16, ["model-value"])) : (vue.openBlock(), vue.createElementBlock(
                    vue.Fragment,
                    { key: 2 },
                    [
                      vue.createTextVNode(
                        vue.toDisplayString(item == null ? void 0 : item.label),
                        1
                        /* TEXT */
                      )
                    ],
                    64
                    /* STABLE_FRAGMENT */
                  ))
                ]),
                _: 2
                /* DYNAMIC */
              }, 1040, ["label", "onClick", "onChange"]);
            }),
            128
            /* KEYED_FRAGMENT */
          )) : (vue.openBlock(), vue.createElementBlock(
            vue.Fragment,
            { key: 1 },
            [
              vue.createCommentVNode(" element-plus \u7248\u672C\u53F7\u5927\u4E8E\u7B49\u4E8E2.6.0 "),
              (vue.openBlock(true), vue.createElementBlock(
                vue.Fragment,
                null,
                vue.renderList(_ctx.options, (item) => {
                  return vue.openBlock(), vue.createBlock(vue.unref(elementPlus.ElRadio), vue.mergeProps({
                    key: `${item.label}${item.value}`,
                    ref_for: true,
                    ref_key: "radioInstance",
                    ref: radioInstance,
                    value: item.value
                  }, vue.unref(is.isFunction)(item.fieldItemProps) ? item.fieldItemProps(item) : item.fieldItemProps, {
                    onClick: ($event) => radioClick(
                      $event,
                      item.value,
                      vue.unref(is.isFunction)(item.fieldItemProps) ? item.fieldItemProps(item) : item.fieldItemProps
                    ),
                    onChange: ($event) => change(item.value)
                  }), {
                    default: vue.withCtx(() => [
                      vue.unref(is.isFunction)(item.fieldSlot) ? (vue.openBlock(), vue.createBlock(vue.resolveDynamicComponent(item.fieldSlot), vue.mergeProps({
                        key: 0,
                        "model-value": state.radio,
                        column: props
                      }, item), null, 16, ["model-value"])) : vue.unref(is.isFunction)(_ctx.fieldChildrenSlot) ? (vue.openBlock(), vue.createBlock(vue.resolveDynamicComponent(_ctx.fieldChildrenSlot), vue.mergeProps({
                        key: 1,
                        "model-value": state.radio,
                        column: props
                      }, item), null, 16, ["model-value"])) : (vue.openBlock(), vue.createElementBlock(
                        vue.Fragment,
                        { key: 2 },
                        [
                          vue.createTextVNode(
                            vue.toDisplayString(item == null ? void 0 : item.label),
                            1
                            /* TEXT */
                          )
                        ],
                        64
                        /* STABLE_FRAGMENT */
                      ))
                    ]),
                    _: 2
                    /* DYNAMIC */
                  }, 1040, ["value", "onClick", "onChange"]);
                }),
                128
                /* KEYED_FRAGMENT */
              ))
            ],
            64
            /* STABLE_FRAGMENT */
          ))
        ]),
        _: 2
        /* DYNAMIC */
      }, [
        vue.renderList(_ctx.fieldSlots, (fieldSlot, key) => {
          return {
            name: key,
            fn: vue.withCtx((data) => [
              (vue.openBlock(), vue.createBlock(
                vue.resolveDynamicComponent(fieldSlot),
                vue.normalizeProps(vue.guardReactiveProps(data)),
                null,
                16
                /* FULL_PROPS */
              ))
            ])
          };
        })
      ]), 1040, ["modelValue"]);
    };
  }
});

exports.default = _sfc_main;
