{"version": 3, "file": "TrieNode.js", "sources": ["../../../../../../src/matcher/dictionary/variants/matching/unmunger/TrieNode.ts"], "sourcesContent": ["export default class TrieNode {\n    constructor(parents = []) {\n        this.parents = parents;\n        // eslint-disable-next-line no-use-before-define\n        this.children = new Map();\n    }\n    addSub(key, ...subs) {\n        const firstChar = key.charAt(0);\n        if (!this.children.has(firstChar)) {\n            this.children.set(firstChar, new TrieNode([...this.parents, firstChar]));\n        }\n        let cur = this.children.get(firstChar);\n        for (let i = 1; i < key.length; i += 1) {\n            const c = key.charAt(i);\n            if (!cur.hasChild(c)) {\n                cur.addChild(c);\n            }\n            cur = cur.getChild(c);\n        }\n        cur.subs = (cur.subs || []).concat(subs);\n        return this;\n    }\n    getChild(child) {\n        return this.children.get(child);\n    }\n    isTerminal() {\n        return !!this.subs;\n    }\n    addChild(child) {\n        if (!this.hasChild(child)) {\n            this.children.set(child, new TrieNode([...this.parents, child]));\n        }\n    }\n    hasChild(child) {\n        return this.children.has(child);\n    }\n}\n//# sourceMappingURL=TrieNode.js.map"], "names": ["TrieNode", "constructor", "parents", "children", "Map", "addSub", "key", "subs", "firstChar", "char<PERSON>t", "has", "set", "cur", "get", "i", "length", "c", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "concat", "child", "isTerminal"], "mappings": ";;AAAe,MAAMA,QAAQ,CAAC;AAC1BC,EAAAA,WAAWA,CAACC,OAAO,GAAG,EAAE,EAAE;IACtB,IAAI,CAACA,OAAO,GAAGA,OAAO,CAAA;AACtB;AACA,IAAA,IAAI,CAACC,QAAQ,GAAG,IAAIC,GAAG,EAAE,CAAA;AAC7B,GAAA;AACAC,EAAAA,MAAMA,CAACC,GAAG,EAAE,GAAGC,IAAI,EAAE;AACjB,IAAA,MAAMC,SAAS,GAAGF,GAAG,CAACG,MAAM,CAAC,CAAC,CAAC,CAAA;IAC/B,IAAI,CAAC,IAAI,CAACN,QAAQ,CAACO,GAAG,CAACF,SAAS,CAAC,EAAE;AAC/B,MAAA,IAAI,CAACL,QAAQ,CAACQ,GAAG,CAACH,SAAS,EAAE,IAAIR,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACE,OAAO,EAAEM,SAAS,CAAC,CAAC,CAAC,CAAA;AAC5E,KAAA;IACA,IAAII,GAAG,GAAG,IAAI,CAACT,QAAQ,CAACU,GAAG,CAACL,SAAS,CAAC,CAAA;AACtC,IAAA,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,GAAG,CAACS,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;AACpC,MAAA,MAAME,CAAC,GAAGV,GAAG,CAACG,MAAM,CAACK,CAAC,CAAC,CAAA;AACvB,MAAA,IAAI,CAACF,GAAG,CAACK,QAAQ,CAACD,CAAC,CAAC,EAAE;AAClBJ,QAAAA,GAAG,CAACM,QAAQ,CAACF,CAAC,CAAC,CAAA;AACnB,OAAA;AACAJ,MAAAA,GAAG,GAAGA,GAAG,CAACO,QAAQ,CAACH,CAAC,CAAC,CAAA;AACzB,KAAA;AACAJ,IAAAA,GAAG,CAACL,IAAI,GAAG,CAACK,GAAG,CAACL,IAAI,IAAI,EAAE,EAAEa,MAAM,CAACb,IAAI,CAAC,CAAA;AACxC,IAAA,OAAO,IAAI,CAAA;AACf,GAAA;EACAY,QAAQA,CAACE,KAAK,EAAE;AACZ,IAAA,OAAO,IAAI,CAAClB,QAAQ,CAACU,GAAG,CAACQ,KAAK,CAAC,CAAA;AACnC,GAAA;AACAC,EAAAA,UAAUA,GAAG;AACT,IAAA,OAAO,CAAC,CAAC,IAAI,CAACf,IAAI,CAAA;AACtB,GAAA;EACAW,QAAQA,CAACG,KAAK,EAAE;AACZ,IAAA,IAAI,CAAC,IAAI,CAACJ,QAAQ,CAACI,KAAK,CAAC,EAAE;AACvB,MAAA,IAAI,CAAClB,QAAQ,CAACQ,GAAG,CAACU,KAAK,EAAE,IAAIrB,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACE,OAAO,EAAEmB,KAAK,CAAC,CAAC,CAAC,CAAA;AACpE,KAAA;AACJ,GAAA;EACAJ,QAAQA,CAACI,KAAK,EAAE;AACZ,IAAA,OAAO,IAAI,CAAClB,QAAQ,CAACO,GAAG,CAACW,KAAK,CAAC,CAAA;AACnC,GAAA;AACJ;;;;"}