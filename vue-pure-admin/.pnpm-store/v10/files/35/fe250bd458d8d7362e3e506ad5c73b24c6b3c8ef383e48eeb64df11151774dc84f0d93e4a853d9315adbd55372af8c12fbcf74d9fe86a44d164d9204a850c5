{"name": "postcss-sorting", "version": "9.1.0", "description": "PostCSS plugin to keep rules and at-rules content in order.", "keywords": ["postcss", "css", "postcss-plugin", "sorting", "order"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": "hudochenkov/postcss-sorting", "bugs": {"url": "https://github.com/hudochenkov/postcss-sorting/issues"}, "homepage": "https://github.com/hudochenkov/postcss-sorting", "files": ["lib", "!lib/**/__tests__", "index.js", "!.DS_Store"], "peerDependencies": {"postcss": "^8.4.20"}, "devDependencies": {"eslint": "^8.31.0", "eslint-config-hudochenkov": "^9.0.0", "eslint-config-prettier": "^8.6.0", "husky": "^9.1.6", "jest": "^29.7.0", "jest-watch-typeahead": "^2.2.2", "lint-staged": "^15.2.10", "postcss": "^8.4.47", "postcss-html": "^1.7.0", "postcss-styled-syntax": "^0.6.4", "prettier": "^3.3.3", "prettier-config-hudochenkov": "^0.4.0"}, "scripts": {"test-full": "npm run lint && jest", "test": "jest", "watch": "jest --watch", "coverage": "jest --coverage", "fix": "eslint *.js 'lib/**/*.js' --max-warnings 0 --fix && prettier --write *.js 'lib/**/*.js'", "lint": "eslint *.js 'lib/**/*.js' --max-warnings 0 && prettier --check *.js 'lib/**/*.js'", "prepare": "husky"}, "lint-staged": {"*.js": ["eslint --fix --max-warnings=0", "prettier --write"]}, "jest": {"setupFiles": ["./jest-setup.js"], "testEnvironment": "node", "testRegex": "__tests__/[a-zA-Z-.]+\\.js$", "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"]}, "prettier": "prettier-config-hudo<PERSON>v"}