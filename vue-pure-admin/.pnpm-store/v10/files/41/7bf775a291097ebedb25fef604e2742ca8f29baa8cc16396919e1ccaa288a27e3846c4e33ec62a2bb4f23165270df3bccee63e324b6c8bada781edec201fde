class t{constructor(){this.listeners={}}on(t,s,e){if(this.listeners[t]||(this.listeners[t]=new Set),this.listeners[t].add(s),null==e?void 0:e.once){const e=()=>{this.un(t,e),this.un(t,s)};return this.on(t,e),e}return()=>this.un(t,s)}un(t,s){var e;null===(e=this.listeners[t])||void 0===e||e.delete(s)}once(t,s){return this.on(t,s,{once:!0})}unAll(){this.listeners={}}emit(t,...s){this.listeners[t]&&this.listeners[t].forEach((t=>t(...s)))}}class s extends t{constructor(t){super(),this.subscriptions=[],this.isDestroyed=!1,this.options=t}onInit(){}_init(t){this.isDestroyed&&(this.subscriptions=[],this.isDestroyed=!1),this.wavesurfer=t,this.onInit()}destroy(){this.emit("destroy"),this.subscriptions.forEach((t=>t())),this.subscriptions=[],this.isDestroyed=!0,this.wavesurfer=void 0}}const e={scale:.5,deltaThreshold:5,exponentialZooming:!1,iterations:20};class i extends s{constructor(t){super(t||{}),this.wrapper=void 0,this.container=null,this.accumulatedDelta=0,this.pointerTime=0,this.oldX=0,this.endZoom=0,this.startZoom=0,this.onWheel=t=>{if(this.wavesurfer&&this.container&&!(Math.abs(t.deltaX)>=Math.abs(t.deltaY))&&(t.preventDefault(),this.accumulatedDelta+=-t.deltaY,0===this.startZoom&&this.options.exponentialZooming&&(this.startZoom=this.wavesurfer.getWrapper().clientWidth/this.wavesurfer.getDuration()),0===this.options.deltaThreshold||Math.abs(this.accumulatedDelta)>=this.options.deltaThreshold)){const s=this.wavesurfer.getDuration(),e=0===this.wavesurfer.options.minPxPerSec?this.wavesurfer.getWrapper().scrollWidth/s:this.wavesurfer.options.minPxPerSec,i=t.clientX-this.container.getBoundingClientRect().left,o=this.container.clientWidth,n=this.wavesurfer.getScroll();i===this.oldX&&0!==this.oldX||(this.pointerTime=(n+i)/e),this.oldX=i;const r=this.calculateNewZoom(e,this.accumulatedDelta),h=o/r*(i/o);r*s<o?(this.wavesurfer.zoom(o/s),this.container.scrollLeft=0):(this.wavesurfer.zoom(r),this.container.scrollLeft=(this.pointerTime-h)*r),this.accumulatedDelta=0}},this.calculateNewZoom=(t,s)=>{let e;if(this.options.exponentialZooming){const i=s>0?Math.pow(this.endZoom/this.startZoom,1/(this.options.iterations-1)):Math.pow(this.startZoom/this.endZoom,1/(this.options.iterations-1));e=Math.max(0,t*i)}else e=Math.max(0,t+s*this.options.scale);return Math.min(e,this.options.maxZoom)},this.options=Object.assign({},e,t)}static create(t){return new i(t)}onInit(){var t;this.wrapper=null===(t=this.wavesurfer)||void 0===t?void 0:t.getWrapper(),this.wrapper&&(this.container=this.wrapper.parentElement,this.container.addEventListener("wheel",this.onWheel),void 0===this.options.maxZoom&&(this.options.maxZoom=this.container.clientWidth),this.endZoom=this.options.maxZoom)}destroy(){this.wrapper&&this.wrapper.removeEventListener("wheel",this.onWheel),super.destroy()}}export{i as default};
