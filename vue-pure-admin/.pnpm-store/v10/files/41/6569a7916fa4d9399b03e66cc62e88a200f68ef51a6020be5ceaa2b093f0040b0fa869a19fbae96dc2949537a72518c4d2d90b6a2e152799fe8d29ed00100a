// NOTICE: This file is generated by Rollup. To modify it,
// please instead edit the ESM counterpart and rebuild with Rollup (npm run build).
'use strict';

const HAS_DIMENSION_LIKE_VALUES = /\d[%\w-]/;

/**
 * Check if a value contains any dimension-like values.
 *
 * @param {string} value
 * @returns {boolean}
 */
function hasDimension(value) {
	return HAS_DIMENSION_LIKE_VALUES.test(value);
}

module.exports = hasDimension;
