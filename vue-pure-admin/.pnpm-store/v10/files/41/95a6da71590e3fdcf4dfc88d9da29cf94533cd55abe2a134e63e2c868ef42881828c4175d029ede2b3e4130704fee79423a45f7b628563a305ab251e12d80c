'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var breadcrumb$1 = require('./src/breadcrumb2.js');
var breadcrumbItem$1 = require('./src/breadcrumb-item2.js');
var breadcrumb = require('./src/breadcrumb.js');
var breadcrumbItem = require('./src/breadcrumb-item.js');
var constants = require('./src/constants.js');
var install = require('../../utils/vue/install.js');

const ElBreadcrumb = install.withInstall(breadcrumb$1["default"], {
  BreadcrumbItem: breadcrumbItem$1["default"]
});
const ElBreadcrumbItem = install.withNoopInstall(breadcrumbItem$1["default"]);

exports.breadcrumbProps = breadcrumb.breadcrumbProps;
exports.breadcrumbItemProps = breadcrumbItem.breadcrumbItemProps;
exports.breadcrumbKey = constants.breadcrumbKey;
exports.ElBreadcrumb = ElBreadcrumb;
exports.ElBreadcrumbItem = ElBreadcrumbItem;
exports["default"] = ElBreadcrumb;
//# sourceMappingURL=index.js.map
