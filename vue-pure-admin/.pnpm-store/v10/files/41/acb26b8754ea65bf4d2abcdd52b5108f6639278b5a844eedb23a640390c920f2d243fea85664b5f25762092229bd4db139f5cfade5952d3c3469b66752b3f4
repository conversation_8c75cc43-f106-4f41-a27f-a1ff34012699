@use 'mixins/mixins' as *;
@use 'mixins/function' as *;
@use 'mixins/config' as *;

@include b(display-item) {
  @include e(edit-icon, true) {
    margin-left: 8px;
  }
  @include e(image, true) {
    cursor: pointer;
    object-fit: cover;
    overflow: hidden;
    width: 30px;
  }
  @include e(icon__copy, true) {
    cursor: pointer;
    color: getCssVar('color-primary');
    text-decoration: none;
    outline: none;
    transition: color 0.3s;
    margin-inline-start: 4px;
    vertical-align: sub;
  }
  @include e(pre, true) {
    padding: 0;
    overflow: auto;
    font-size: 85%;
    line-height: 1.45;
    background-color: getCssVar('color-primary-light-9');
    border-radius: 3px;
    width: unset;
  }
  @include e(badge, true) {
    &.is-list {
      .#{$plus-namespace
        + -display-item__badge__item}
        + .#{$plus-namespace
        + -display-item__badge__item} {
        padding-left: 8px;
      }
    }

    @include e(badge__dot) {
      position: relative;
      top: -1px;
      display: inline-block;
      width: 6px;
      height: 6px;
      vertical-align: middle;
      border-radius: 50%;

      &--primary {
        background-color: getCssVar('color-primary');
      }
      &--success {
        background-color: getCssVar('color-success');
      }
      &--info {
        background-color: getCssVar('color-info');
      }
      &--warning {
        background-color: getCssVar('color-warning');
      }
      &--danger {
        background-color: getCssVar('color-danger');
      }
    }
  }
  @include e(form, true) {
    .#{$plus-namespace + -form-item} {
      margin-bottom: 0;
    }
  }

  @include e(link, true) {
    max-width: 100%;
    @include elb(link__inner) {
      width: 100%;
      display: inline-block;
    }
  }
}
