{"version": 3, "file": "url-match.js", "sourceRoot": "", "sources": ["../../../src/match/url-match.ts"], "names": [], "mappings": ";;;;AAAA,iCAA6C;AAG7C;;;;;;;GAOG;AACH;IAA8B,yCAAK;IA4F/B;;;;OAIG;IACH,kBAAY,GAAmB;QAA/B,YACI,kBAAM,GAAG,CAAC,SASb;QA1GD;;;;WAIG;QACK,SAAG,GAAW,EAAE,CAAC,CAAC,gGAAgG;QAE1H;;;;;;;WAOG;QACc,kBAAY,GAAwB,QAAQ,CAAC,CAAC,gGAAgG;QAE/J;;;;;WAKG;QACc,sBAAgB,GAAY,KAAK,CAAC,CAAC,gGAAgG;QAEpJ;;;;;;WAMG;QACc,2BAAqB,GAAY,KAAK,CAAC,CAAC,gGAAgG;QAEzJ;;;;WAIG;QACc,iBAAW,GAAmC;YAC3D,MAAM,EAAE,IAAI;YACZ,GAAG,EAAE,IAAI;SACZ,CAAC,CAAC,gGAAgG;QAEnG;;;WAGG;QACc,wBAAkB,GAAY,IAAI,CAAC,CAAC,gGAAgG;QAErJ;;;WAGG;QACc,2BAAqB,GAAY,IAAI,CAAC,CAAC,gGAAgG;QAExJ;;;;;;WAMG;QACH,uBAAiB,GAAG,kBAAkB,CAAC;QAEvC;;;;;WAKG;QACH,oBAAc,GAAG,0BAA0B,CAAC;QAE5C;;;;;;WAMG;QACH,2BAAqB,GAAG,OAAO,CAAC;QAEhC;;;;;;WAMG;QACH,uBAAiB,GAAG,KAAK,CAAC;QAUtB,KAAI,CAAC,YAAY,GAAG,GAAG,CAAC,YAAY,CAAC;QACrC,KAAI,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;QACnB,KAAI,CAAC,gBAAgB,GAAG,GAAG,CAAC,gBAAgB,CAAC;QAC7C,KAAI,CAAC,qBAAqB,GAAG,GAAG,CAAC,qBAAqB,CAAC;QACvD,KAAI,CAAC,WAAW,GAAG,GAAG,CAAC,WAAW,CAAC;QACnC,KAAI,CAAC,kBAAkB,GAAG,GAAG,CAAC,kBAAkB,CAAC;QACjD,KAAI,CAAC,qBAAqB,GAAG,GAAG,CAAC,qBAAqB,CAAC;;IAC3D,CAAC;IAED;;;;;OAKG;IACH,0BAAO,GAAP;QACI,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;;;;;OAUG;IACH,kCAAe,GAAf;QACI,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;;;;OAKG;IACH,yBAAM,GAAN;QACI,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QAEnB,oEAAoE;QACpE,IAAI,CAAC,IAAI,CAAC,qBAAqB,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAClF,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,SAAS,GAAG,GAAG,CAAC;YAEjC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;SACjC;QAED,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;;;OAIG;IACH,gCAAa,GAAb;QACI,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAExB,OAAO,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,4GAA4G;IACnJ,CAAC;IAED;;;;OAIG;IACH,gCAAa,GAAb;QACI,IAAI,UAAU,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAEvC,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,4DAA4D;YAC5D,UAAU,GAAG,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC,CAAC;SAC7D;QACD,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;YACzB,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;SACnD;QACD,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE;YACtB,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;SAChD;QACD,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC,CAAC,yCAAyC;SAC/F;QACD,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;SACvD;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAED,0CAA0C;IAE1C,wBAAwB;IAExB;;;;;;;;OAQG;IACK,oCAAiB,GAAzB,UAA0B,GAAW;QACjC,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;IACnD,CAAC;IAED;;;;;;;OAOG;IACK,iCAAc,GAAtB,UAAuB,GAAW;QAC9B,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC,CAAC,uCAAuC;IAC1F,CAAC;IAED;;;;;;;OAOG;IACK,8CAA2B,GAAnC,UAAoC,IAAY;QAC5C,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC,CAAC;IACxD,CAAC;IAED;;;;;;;OAOG;IACK,sCAAmB,GAA3B,UAA4B,UAAkB;QAC1C,IAAI,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;YAClD,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SACxC;QACD,OAAO,UAAU,CAAC;IACtB,CAAC;IAED;;;;;;;;;OASG;IACK,wCAAqB,GAA7B,UAA8B,UAAkB;QAC5C,qEAAqE;QACrE,mEAAmE;QACnE,kBAAkB;QAClB,IAAM,4BAA4B,GAAG,UAAU;aAC1C,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,SAAS;aACpC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,SAAS;aACnC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,SAAS;aACnC,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,SAAS;aAClC,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC,SAAS;QAExC,IAAI;YACA,oDAAoD;YACpD,OAAO,kBAAkB,CAAC,4BAA4B,CAAC,CAAC;SAC3D;QAAC,OAAO,CAAC,EAAE;YACR,+CAA+C;YAC/C,OAAO,4BAA4B,CAAC;SACvC;IACL,CAAC;IACL,eAAC;AAAD,CAAC,AArRD,CAA8B,aAAK,GAqRlC;AArRY,4BAAQ"}