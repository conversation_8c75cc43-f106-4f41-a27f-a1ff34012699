import { RenderTypes } from 'plus-pro-components';
import { CreateComponentPublicInstance, ExtractPropTypes, PropType, ComponentOptionsMixin, VNodeProps, AllowedComponentProps, ComponentCustomProps, ComponentOptionsBase } from 'vue';
export * from './src/type';
export declare const PlusHeader: {
    new (...args: any[]): CreateComponentPublicInstance<Readonly< ExtractPropTypes<{
        fixed: {
            type: PropType<boolean>;
            default: boolean;
        };
        trigger: {
            type: PropType<"hover" | "click">;
            default: string;
        };
        title: {
            type: PropType<string>;
            default: string;
        };
        logo: {
            type: PropType<string>;
            default: string;
        };
        logoutText: {
            type: PropType<string>;
            default: string;
        };
        userInfo: {
            type: PropType<{
                [index: string]: unknown;
                [index: number]: unknown;
                [index: symbol]: unknown;
                username?: string | undefined;
                avatar?: string | undefined;
            }>;
            default: () => {};
        };
        hasUserInfo: {
            type: PropType<boolean>;
            default: boolean;
        };
        dropdownList: {
            type: PropType<{
                label: string;
                value: string;
            }[]>;
            default: () => never[];
        };
        renderHeaderLeft: {
            type: PropType<(info: {
                logo: string;
                title: string;
            }) => RenderTypes>;
        };
        renderHeaderRight: {
            type: PropType<(info: {
                userInfo: Record<string | number | symbol, unknown>;
                title: string;
            }) => RenderTypes>;
        };
    }>> & {
        onClickDropdownItem?: ((item: {
            label: string;
            value: string;
        }) => any) | undefined;
    }, {}, unknown, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
        clickDropdownItem: (item: {
            label: string;
            value: string;
        }) => void;
    }, VNodeProps & AllowedComponentProps & ComponentCustomProps & Readonly< ExtractPropTypes<{
        fixed: {
            type: PropType<boolean>;
            default: boolean;
        };
        trigger: {
            type: PropType<"hover" | "click">;
            default: string;
        };
        title: {
            type: PropType<string>;
            default: string;
        };
        logo: {
            type: PropType<string>;
            default: string;
        };
        logoutText: {
            type: PropType<string>;
            default: string;
        };
        userInfo: {
            type: PropType<{
                [index: string]: unknown;
                [index: number]: unknown;
                [index: symbol]: unknown;
                username?: string | undefined;
                avatar?: string | undefined;
            }>;
            default: () => {};
        };
        hasUserInfo: {
            type: PropType<boolean>;
            default: boolean;
        };
        dropdownList: {
            type: PropType<{
                label: string;
                value: string;
            }[]>;
            default: () => never[];
        };
        renderHeaderLeft: {
            type: PropType<(info: {
                logo: string;
                title: string;
            }) => RenderTypes>;
        };
        renderHeaderRight: {
            type: PropType<(info: {
                userInfo: Record<string | number | symbol, unknown>;
                title: string;
            }) => RenderTypes>;
        };
    }>> & {
        onClickDropdownItem?: ((item: {
            label: string;
            value: string;
        }) => any) | undefined;
    }, {
        fixed: boolean;
        trigger: "hover" | "click";
        title: string;
        logo: string;
        logoutText: string;
        userInfo: {
            [index: string]: unknown;
            [index: number]: unknown;
            [index: symbol]: unknown;
            username?: string | undefined;
            avatar?: string | undefined;
        };
        hasUserInfo: boolean;
        dropdownList: {
            label: string;
            value: string;
        }[];
    }, true, {}, {}, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly< ExtractPropTypes<{
        fixed: {
            type: PropType<boolean>;
            default: boolean;
        };
        trigger: {
            type: PropType<"hover" | "click">;
            default: string;
        };
        title: {
            type: PropType<string>;
            default: string;
        };
        logo: {
            type: PropType<string>;
            default: string;
        };
        logoutText: {
            type: PropType<string>;
            default: string;
        };
        userInfo: {
            type: PropType<{
                [index: string]: unknown;
                [index: number]: unknown;
                [index: symbol]: unknown;
                username?: string | undefined;
                avatar?: string | undefined;
            }>;
            default: () => {};
        };
        hasUserInfo: {
            type: PropType<boolean>;
            default: boolean;
        };
        dropdownList: {
            type: PropType<{
                label: string;
                value: string;
            }[]>;
            default: () => never[];
        };
        renderHeaderLeft: {
            type: PropType<(info: {
                logo: string;
                title: string;
            }) => RenderTypes>;
        };
        renderHeaderRight: {
            type: PropType<(info: {
                userInfo: Record<string | number | symbol, unknown>;
                title: string;
            }) => RenderTypes>;
        };
    }>> & {
        onClickDropdownItem?: ((item: {
            label: string;
            value: string;
        }) => any) | undefined;
    }, {}, {}, {}, {}, {
        fixed: boolean;
        trigger: "hover" | "click";
        title: string;
        logo: string;
        logoutText: string;
        userInfo: {
            [index: string]: unknown;
            [index: number]: unknown;
            [index: symbol]: unknown;
            username?: string | undefined;
            avatar?: string | undefined;
        };
        hasUserInfo: boolean;
        dropdownList: {
            label: string;
            value: string;
        }[];
    }>;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
} & ComponentOptionsBase<Readonly< ExtractPropTypes<{
    fixed: {
        type: PropType<boolean>;
        default: boolean;
    };
    trigger: {
        type: PropType<"hover" | "click">;
        default: string;
    };
    title: {
        type: PropType<string>;
        default: string;
    };
    logo: {
        type: PropType<string>;
        default: string;
    };
    logoutText: {
        type: PropType<string>;
        default: string;
    };
    userInfo: {
        type: PropType<{
            [index: string]: unknown;
            [index: number]: unknown;
            [index: symbol]: unknown;
            username?: string | undefined;
            avatar?: string | undefined;
        }>;
        default: () => {};
    };
    hasUserInfo: {
        type: PropType<boolean>;
        default: boolean;
    };
    dropdownList: {
        type: PropType<{
            label: string;
            value: string;
        }[]>;
        default: () => never[];
    };
    renderHeaderLeft: {
        type: PropType<(info: {
            logo: string;
            title: string;
        }) => RenderTypes>;
    };
    renderHeaderRight: {
        type: PropType<(info: {
            userInfo: Record<string | number | symbol, unknown>;
            title: string;
        }) => RenderTypes>;
    };
}>> & {
    onClickDropdownItem?: ((item: {
        label: string;
        value: string;
    }) => any) | undefined;
}, {}, unknown, {}, {}, ComponentOptionsMixin, ComponentOptionsMixin, {
    clickDropdownItem: (item: {
        label: string;
        value: string;
    }) => void;
}, string, {
    fixed: boolean;
    trigger: "hover" | "click";
    title: string;
    logo: string;
    logoutText: string;
    userInfo: {
        [index: string]: unknown;
        [index: number]: unknown;
        [index: symbol]: unknown;
        username?: string | undefined;
        avatar?: string | undefined;
    };
    hasUserInfo: boolean;
    dropdownList: {
        label: string;
        value: string;
    }[];
}, {}, string, {}> & VNodeProps & AllowedComponentProps & ComponentCustomProps & (new () => {
    $slots: {
        "header-left"?(_: {
            logo: string;
            title: string;
        }): any;
        "header-right"?(_: {
            userInfo: {
                [index: string]: unknown;
                [index: number]: unknown;
                [index: symbol]: unknown;
                username?: string | undefined;
                avatar?: string | undefined;
            };
            title: string;
        }): any;
    };
});
