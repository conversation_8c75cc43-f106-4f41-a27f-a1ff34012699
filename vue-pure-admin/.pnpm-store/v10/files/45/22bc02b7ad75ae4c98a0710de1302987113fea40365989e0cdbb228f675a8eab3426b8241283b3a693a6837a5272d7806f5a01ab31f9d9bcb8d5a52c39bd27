/*! plus-pro-components v0.1.24 */var e={name:"en",plus:{dialog:{confirmText:"Yes",cancelText:"No",title:"Dialog"},datepicker:{startPlaceholder:"Please select start time",endPlaceholder:"Please select end time"},dialogForm:{title:"Dialog form"},drawerForm:{title:"Drawer form",confirmText:"Yes",cancelText:"No"},form:{submitText:"Submit",resetText:"Reset",errorTip:"Please complete the form and submit again!"},field:{pleaseEnter:"Please enter ",pleaseSelect:"Please select "},popover:{confirmText:"Yes",cancelText:"No"},search:{searchText:"Search",resetText:"Reset",expand:"Expand",retract:"Retract"},table:{title:"Table",density:"Density",refresh:"Refresh",columnSettings:"Column settings",selectAll:"Select all",default:"Default",loose:"Loose",compact:"Compact",action:"Action",more:"More",confirmToPerformThisOperation:"Confirm to perform this operation?",prompt:"Prompt",sort:"Sort",resetText:"Reset"},stepsForm:{nextText:"Next step",preText:"Previous step",submitText:"Submit"},inputTag:{placeholder:"Please enter keywords and press enter or space key"},header:{logout:"logout"}}};export{e as default};
