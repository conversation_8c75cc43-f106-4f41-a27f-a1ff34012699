const objectToString = Object.prototype.toString;
const toTypeString = (value) => objectToString.call(value);
const toRawType = (value) => {
  return toTypeString(value).slice(8, -1);
};
const isArray = Array.isArray;
const isMap = (val) => toTypeString(val) === "[object Map]";
const isSet = (val) => toTypeString(val) === "[object Set]";
const isDate = (val) => toTypeString(val) === "[object Date]";
const isRegExp = (val) => toTypeString(val) === "[object RegExp]";
const isFunction = (val) => typeof val === "function";
const isString = (val) => typeof val === "string";
const isSymbol = (val) => typeof val === "symbol";
const isBoolean = (val) => typeof val === "boolean";
const isObject = (val) => val !== null && typeof val === "object";
const isPromise = (val) => {
  return isObject(val) && isFunction(val.then) && isFunction(val.catch);
};
const isPlainObject = (val) => toTypeString(val) === "[object Object]";
const isEmptyObject = (val) => isPlainObject(val) && Object.keys(val).length === 0;
function isUrl(url) {
  const regex = new RegExp(
    "^(https?:\\/\\/)?((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|((\\d{1,3}\\.){3}\\d{1,3}))(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*(\\?[;&a-z\\d%_.~+=-]*)?(\\#[-a-z\\d_]*)?$",
    "i"
  );
  return regex.test(url);
}
const isSVGElement = (tag) => typeof SVGElement !== "undefined" && tag instanceof SVGElement;

export { isArray, isBoolean, isDate, isEmptyObject, isFunction, isMap, isObject, isPlainObject, isPromise, isRegExp, isSVGElement, isSet, isString, isSymbol, isUrl, objectToString, toRawType, toTypeString };
