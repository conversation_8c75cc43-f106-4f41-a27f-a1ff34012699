# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [1.0.2](https://github.com/wangeditor-team/wangEditor/compare/@wangeditor/upload-image-module@1.0.1...@wangeditor/upload-image-module@1.0.2) (2022-09-15)


### Bug Fixes

* customInsert 不触发 onSuccess ([d6f4a1b](https://github.com/wangeditor-team/wangEditor/commit/d6f4a1b1494864b116a1310cce2d9e8632c92c6f))





## 1.0.1 (2022-04-18)


### Bug Fixes

* 多图片上传 ([53fe915](https://github.com/wangeditor-team/wangEditor/commit/53fe915aa7d40f05e1e9446c7f26606c46832ff3))
* 更新各包之间依赖版本 ([75c552c](https://github.com/wangeditor-team/wangEditor/commit/75c552cc8ed54765bebb86a7ec5329a7fc79e85f))
* 上传图片 - base64 仍触发上传 + 超出 maxSize 的报错提醒 ([a1d469a](https://github.com/wangeditor-team/wangEditor/commit/a1d469accb7f87f8ea0282a1699d002aaaa4e79a))
* 图片上传，提示 ([3754012](https://github.com/wangeditor-team/wangEditor/commit/37540129dff1212c5ebfd4ca3f4d4e8def735e73))
* 修复 pnpm 安装 @wangeditor/editor 出现警告的问题 ([4087fbe](https://github.com/wangeditor-team/wangEditor/commit/4087fbee01c76bdd55e747a5e86c5e4a8d6a8353))
* 移除了每个包下的 publishConfig directory 配置 ([16559f0](https://github.com/wangeditor-team/wangEditor/commit/16559f052545c111318be760e64291a521bdcc65))
* 粘贴 excel ([5382a6e](https://github.com/wangeditor-team/wangEditor/commit/5382a6edab2d362c7be143b62e7dd21bea8a15ab))
* npm install error - basic-modules 相关 ([b85a0dc](https://github.com/wangeditor-team/wangEditor/commit/b85a0dcfaa15d69424d86a20255d6b9e8b28494f))
* rename es module filename ([1821d4e](https://github.com/wangeditor-team/wangEditor/commit/1821d4eef49e64efcb41b848849ca7a5e6472044))


### Features

* 上传图片 metaWithUrl ([2485157](https://github.com/wangeditor-team/wangEditor/commit/24851576a1dcc07b1a8931d17a147c3640222e85))
* 增加 enable disable API（删除 setConfig setMenuConfig API） ([984fc50](https://github.com/wangeditor-team/wangEditor/commit/984fc50520061fc34ea08f4136bdeb93dee46564))
* editor.showProgressBar ([51761d4](https://github.com/wangeditor-team/wangEditor/commit/51761d466ab3ef7c99e872954d4724ab51d8e28c))
* i18n ([c11b244](https://github.com/wangeditor-team/wangEditor/commit/c11b2440f91b99d40bca18b675c66a22b6e160c9))
* image menu - width 50% 100% ([f9b4c68](https://github.com/wangeditor-team/wangEditor/commit/f9b4c68dff3232b50491b07949c20eb4c18baa6b))
* image menu config ([bb18774](https://github.com/wangeditor-team/wangEditor/commit/bb187740e9703b4a76cde4f5e4d32ac714aa793a))
* upload image ([0a0564b](https://github.com/wangeditor-team/wangEditor/commit/0a0564bf14edd4dea6eb958e653272a9a216cec1))
* upload video ([ac8e6f8](https://github.com/wangeditor-team/wangEditor/commit/ac8e6f8b5258e593714676a6f6be359ba525833c))
