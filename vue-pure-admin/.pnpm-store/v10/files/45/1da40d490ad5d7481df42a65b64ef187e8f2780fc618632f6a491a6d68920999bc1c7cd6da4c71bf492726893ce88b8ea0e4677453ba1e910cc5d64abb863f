export class ColorConverters {
    static CMYK_G([c, y, m, k]: [any, any, any, any]): (string | number)[];
    static G_CMYK([g]: [any]): (string | number)[];
    static G_RGB([g]: [any]): any[];
    static G_rgb([g]: [any]): any[];
    static G_HTML([g]: [any]): string;
    static RGB_G([r, g, b]: [any, any, any]): (string | number)[];
    static RGB_rgb(color: any): any;
    static RGB_HTML(color: any): string;
    static T_HTML(): string;
    static T_rgb(): null[];
    static CMYK_RGB([c, y, m, k]: [any, any, any, any]): (string | number)[];
    static CMYK_rgb([c, y, m, k]: [any, any, any, any]): number[];
    static CMYK_HTML(components: any): string;
    static RGB_CMYK([r, g, b]: [any, any, any]): (string | number)[];
}
