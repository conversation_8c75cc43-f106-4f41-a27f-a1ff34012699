'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
var Sortable = require('sortablejs');
require('../../utils/index.js');
require('../../../hooks/index.js');
var elementPlus = require('element-plus');
var useLocale = require('../../../hooks/useLocale.js');
var is = require('../../utils/is.js');

const _hoisted_1 = { class: "plus-table-column-drag-icon" };
var _sfc_main = /* @__PURE__ */ vue.defineComponent({
  ...{
    name: "PlusTableColumnDragSort"
  },
  __name: "table-column-drag-sort",
  props: {
    sortable: { type: Boolean, default: true },
    tableInstance: { default: null },
    dragSortableTableColumnProps: { default: () => ({}) }
  },
  emits: ["dragSortEnd"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const { t } = useLocale.useLocale();
    vue.watch(
      () => props.tableInstance,
      (val) => {
        if (val && props.sortable) {
          rowDrop();
        }
      }
    );
    const rowDrop = () => {
      var _a, _b;
      const tbody = (_b = (_a = props.tableInstance) == null ? void 0 : _a.$el) == null ? void 0 : _b.querySelector(".el-table__body-wrapper tbody");
      if (!tbody) return;
      let config = {
        handle: ".plus-table-column-drag-icon",
        animation: 150,
        group: "box",
        easing: "cubic-bezier(1, 0, 0, 1)",
        chosenClass: "sortable-chosen",
        forceFallback: true,
        onEnd({ newIndex, oldIndex }) {
          emit("dragSortEnd", newIndex, oldIndex);
        }
      };
      if (is.isPlainObject(props.sortable)) {
        config = { ...config, ...props.sortable };
      }
      Sortable.create(tbody, config);
    };
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createBlock(vue.unref(elementPlus.ElTableColumn), vue.mergeProps({
        key: "dragSort",
        label: vue.unref(t)("plus.table.sort"),
        width: "60",
        "class-name": "plus-table-column-drag-sort"
      }, _ctx.dragSortableTableColumnProps), {
        default: vue.withCtx(() => [
          vue.createElementVNode("span", _hoisted_1, [
            vue.renderSlot(_ctx.$slots, "drag-sort-icon", {}, () => [
              vue.createTextVNode("\u2637")
            ])
          ])
        ]),
        _: 3
        /* FORWARDED */
      }, 16, ["label"]);
    };
  }
});

exports.default = _sfc_main;
