{"version": 3, "sources": ["container/OtherContainer/Stack.js", "../../src/container/OtherContainer/Stack.ts"], "names": ["Object", "defineProperty", "exports", "value", "default", "_ContainerBase", "require", "<PERSON><PERSON>", "Base", "constructor", "container", "super", "this", "_stack", "self", "for<PERSON>ach", "el", "push", "clear", "_length", "element", "pop", "top", "_default"], "mappings": "AAAA;;AAEAA,OAAOC,eAAeC,SAAS,KAAc;IAC3CC,OAAO;;;AAETD,QAAQE,eAAe;;ACLvB,IAAAC,iBAAAC,QAAA;;AAEA,MAAMC,cAAiBC,eAAAA;IAKrBC,YAAYC,IAA8B;QACxCC;QAFMC,KAAAC,IAAc;QAGpB,MAAMC,IAAOF;QACbF,EAAUK,SAAQ,SAAUC;YAC1BF,EAAKG,KAAKD;ADAR;AACJ;ICEFE;QACEN,KAAKO,IAAU;QACfP,KAAKC,IAAS;ADAd;ICOFI,KAAKG;QACHR,KAAKC,EAAOI,KAAKG;QACjBR,KAAKO,KAAW;QAChB,OAAOP,KAAKO;ADAZ;ICMFE;QACE,IAAIT,KAAKO,MAAY,GAAG;QACxBP,KAAKO,KAAW;QAChB,OAAOP,KAAKC,EAAOQ;ADCnB;ICKFC;QACE,OAAOV,KAAKC,EAAOD,KAAKO,IAAU;ADClC;;;ACCH,IAAAI,WAEchB;;AAAKL,QAAAE,UAAAmB", "file": "Stack.js", "sourcesContent": ["import { Base } from \"../ContainerBase\";\nclass Stack extends Base {\n    constructor(container = []) {\n        super();\n        /**\n         * @internal\n         */\n        this._stack = [];\n        const self = this;\n        container.forEach(function (el) {\n            self.push(el);\n        });\n    }\n    clear() {\n        this._length = 0;\n        this._stack = [];\n    }\n    /**\n     * @description Insert element to stack's end.\n     * @description The element you want to push to the back.\n     * @returns The container length after erasing.\n     */\n    push(element) {\n        this._stack.push(element);\n        this._length += 1;\n        return this._length;\n    }\n    /**\n     * @description Removes the end element.\n     * @returns The element you popped.\n     */\n    pop() {\n        if (this._length === 0)\n            return;\n        this._length -= 1;\n        return this._stack.pop();\n    }\n    /**\n     * @description Accesses the end element.\n     * @returns The last element.\n     */\n    top() {\n        return this._stack[this._length - 1];\n    }\n}\nexport default Stack;\n", "import { Base, initContainer } from '@/container/ContainerBase';\n\nclass Stack<T> extends Base {\n  /**\n   * @internal\n   */\n  private _stack: T[] = [];\n  constructor(container: initContainer<T> = []) {\n    super();\n    const self = this;\n    container.forEach(function (el) {\n      self.push(el);\n    });\n  }\n  clear() {\n    this._length = 0;\n    this._stack = [];\n  }\n  /**\n   * @description Insert element to stack's end.\n   * @description The element you want to push to the back.\n   * @returns The container length after erasing.\n   */\n  push(element: T) {\n    this._stack.push(element);\n    this._length += 1;\n    return this._length;\n  }\n  /**\n   * @description Removes the end element.\n   * @returns The element you popped.\n   */\n  pop() {\n    if (this._length === 0) return;\n    this._length -= 1;\n    return this._stack.pop();\n  }\n  /**\n   * @description Accesses the end element.\n   * @returns The last element.\n   */\n  top(): T | undefined {\n    return this._stack[this._length - 1];\n  }\n}\n\nexport default Stack;\n"]}