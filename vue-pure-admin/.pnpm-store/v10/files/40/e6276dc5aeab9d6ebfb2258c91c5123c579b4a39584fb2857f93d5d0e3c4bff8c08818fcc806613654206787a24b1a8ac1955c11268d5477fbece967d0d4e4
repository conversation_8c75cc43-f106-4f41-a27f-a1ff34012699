"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=void 0;var _xeUtils=_interopRequireDefault(require("xe-utils")),_dom=require("../../tools/dom");function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function getTargetOffset(e,t){let o=0,l=0;var r,n,a=!_dom.browse.firefox&&(0,_dom.hasClass)(e,"vxe-checkbox--label");for(a&&(r=getComputedStyle(e),o-=_xeUtils.default.toNumber(r.paddingTop),l-=_xeUtils.default.toNumber(r.paddingLeft));e&&e!==t;)o+=e.offsetTop,l+=e.offsetLeft,e=e.offsetParent,a&&(n=getComputedStyle(e),o-=_xeUtils.default.toNumber(n.paddingTop),l-=_xeUtils.default.toNumber(n.paddingLeft));return{offsetTop:o,offsetLeft:l}}const tableKeyboardHook={setupTable(k){const{props:g,reactData:y,internalData:H}=k,a=k.getRefMaps()["refElem"],{computeEditOpts:f,computeCheckboxOpts:s,computeMouseOpts:d,computeTreeOpts:u}=k.getComputeMaps();const i=(e,u)=>{var{column:t,cell:o}=u;if("checkbox"===t.type){const r=a.value;var l=H["elemStore"];const i=e.clientX,g=e.clientY;t=l[`${t.fixed||"main"}-body-wrapper`]||l["main-body-wrapper"];const f=t?t.value:null;if(f){const m=f.querySelector(".vxe-table--checkbox-range"),n=document.onmousemove,h=document.onmouseup,v=o.parentNode,p=k.getCheckboxRecords();let c=[];const x=1;l=getTargetOffset(e.target,f);const w=l.offsetTop+e.offsetY,b=l.offsetLeft+e.offsetX,C=f.scrollTop,T=v.offsetHeight;let a=null,s=!1,d=1;const R=(e,t)=>{k.dispatchEvent("checkbox-range-"+e,{records:k.getCheckboxRecords(),reserves:k.getCheckboxReserveRecords()},t)},I=e=>{var{clientX:t,clientY:o}=e,t=t-i,o=o-g+(f.scrollTop-C);let l=Math.abs(o),r=Math.abs(t),n=w,a=b;o<x?(n+=o)<x&&(n=x,l=w):l=Math.min(l,f.scrollHeight-w-x),t<x?(a+=t,r>b&&(a=x,r=b)):r=Math.min(r,f.clientWidth-b-x),m.style.height=l+"px",m.style.width=r+"px",m.style.left=a+"px",m.style.top=n+"px",m.style.display="block";t=function(e,t,o){let l=0,r=[];var n=0<o,a=0<o?o:Math.abs(o)+t.offsetHeight,o=y["scrollYLoad"],{afterFullData:c,scrollYStore:s}=H;if(o){o=k.getVTRowIndex(e.row);r=n?c.slice(o,o+Math.ceil(a/s.rowHeight)):c.slice(o-Math.floor(a/s.rowHeight)+1,o+1)}else for(var d=n?"next":"previous";t&&l<a;){var u=k.getRowNode(t);u&&(r.push(u.item),l+=t.offsetHeight,t=t[d+"ElementSibling"])}return r}(u,v,o<x?-l:l);10<l&&t.length!==c.length&&(c=t,e.ctrlKey?t.forEach(e=>{k.handleSelectRow({row:e},-1===p.indexOf(e))}):(k.setAllCheckboxRow(!1),k.handleCheckedCheckboxRow(t,!0,!1)),R("change",e))},_=()=>{clearTimeout(a),a=null},M=n=>{_(),a=setTimeout(()=>{var e,t,o,l,r;a&&({scrollLeft:e,scrollTop:t,clientHeight:o,scrollHeight:l}=f,r=Math.ceil(50*d/T),s?t+o<l?(k.scrollTo(e,t+r),M(n),I(n)):_():t?(k.scrollTo(e,t-r),M(n),I(n)):_())},50)};(0,_dom.addClass)(r,"drag--range"),document.onmousemove=e=>{e.preventDefault(),e.stopPropagation();var t=e["clientY"],o=(0,_dom.getAbsolutePos)(f)["boundingTop"];t<o?(s=!1,d=o-t,a||M(e)):t>o+f.clientHeight?(s=!0,d=t-o-f.clientHeight,a||M(e)):a&&_(),I(e)},document.onmouseup=e=>{_(),(0,_dom.removeClass)(r,"drag--range"),m.removeAttribute("style"),document.onmousemove=n,document.onmouseup=h,R("end",e)},R("start",e)}}};return{moveTabSelected(e,t,o){var l=g["editConfig"],{afterFullData:r,visibleColumn:n}=H,a=f.value;let c,s,d;const u=Object.assign({},e);var e=k.getVTRowIndex(u.row),i=k.getVTColumnIndex(u.column),t=(o.preventDefault(),t?i<=0?0<e&&(s=e-1,c=r[s],d=n.length-1):d=i-1:i>=n.length-1?e<r.length-1&&(s=e+1,c=r[s],d=0):d=i+1,n[d]);t&&(c?(u.rowIndex=s,u.row=c):u.rowIndex=e,u.columnIndex=d,u.column=t,u.cell=k.getCell(u.row,u.column),l?"click"!==a.trigger&&"dblclick"!==a.trigger||("row"===a.mode?k.handleActived(u,o):k.scrollToRow(u.row,u.column).then(()=>k.handleSelected(u,o))):k.scrollToRow(u.row,u.column).then(()=>k.handleSelected(u,o)))},moveCurrentRow(e,t,o){var l=g["treeConfig"];const r=y["currentRow"];var n=H["afterFullData"],a=u.value,a=a.children||a.childrenField;let c;if(o.preventDefault(),r?l?({index:l,items:a}=_xeUtils.default.findTree(n,e=>e===r,{children:a}),e&&0<l?c=a[l-1]:t&&l<a.length-1&&(c=a[l+1])):(a=k.getVTRowIndex(r),e&&0<a?c=n[a-1]:t&&a<n.length-1&&(c=n[a+1])):c=n[0],c){const s={$table:k,row:c,rowIndex:k.getRowIndex(c),$rowIndex:k.getVMRowIndex(c)};k.scrollToRow(c).then(()=>k.triggerCurrentRowEvent(o,s))}},moveSelected(e,t,o,l,r,n){var{afterFullData:a,visibleColumn:c}=H;const s=Object.assign({},e);var e=k.getVTRowIndex(s.row),d=k.getVTColumnIndex(s.column);n.preventDefault(),o&&0<e?(s.rowIndex=e-1,s.row=a[s.rowIndex]):r&&e<a.length-1?(s.rowIndex=e+1,s.row=a[s.rowIndex]):t&&d?(s.columnIndex=d-1,s.column=c[s.columnIndex]):l&&d<c.length-1&&(s.columnIndex=d+1,s.column=c[s.columnIndex]),k.scrollToRow(s.row,s.column).then(()=>{s.cell=k.getCell(s.row,s.column),k.handleSelected(s,n)})},triggerHeaderCellMousedownEvent(e,t){var o,l=g["mouseConfig"],r=d.value;l&&r.area&&k.handleHeaderCellAreaEvent&&(l=e.currentTarget,r=(0,_dom.getEventTargetNode)(e,l,"vxe-cell--sort").flag,o=(0,_dom.getEventTargetNode)(e,l,"vxe-cell--filter").flag,k.handleHeaderCellAreaEvent(e,Object.assign({cell:l,triggerSort:r,triggerFilter:o},t))),k.focus(),k.closeMenu&&k.closeMenu()},triggerCellMousedownEvent(e,t){var o=e.currentTarget;t.cell=o,((e,t)=>{var{editConfig:o,checkboxConfig:l,mouseConfig:r}=g,n=s.value,a=d.value,c=f.value;if(r&&a.area&&k.handleCellAreaEvent)return k.handleCellAreaEvent(e,t);l&&n.range&&i(e,t),r&&a.selected&&(o&&"cell"!==c.mode||k.handleSelected(t,e))})(e,t),k.focus(),k.closeFilter(),k.closeMenu&&k.closeMenu()}}}};var _default=exports.default=tableKeyboardHook;