{"version": 3, "file": "index.mjs", "sources": ["../src/utils.ts", "../src/validators/native.ts", "../src/validators/custom.ts", "../src/validators/oneof.ts", "../src/validators/oneoftype.ts", "../src/validators/arrayof.ts", "../src/validators/instanceof.ts", "../src/validators/objectof.ts", "../src/validators/shape.ts", "../src/index.ts"], "sourcesContent": ["import './global-this'\nimport { config } from './config'\nimport {\n  VueTypeDef,\n  VueTypeValidableDef,\n  VueProp,\n  InferType,\n  PropOptions,\n  VueTypesConfig,\n  ValidatorFunction,\n} from './types'\nimport { isPlainObject } from './is-plain-obj'\n\nexport { isPlainObject }\n\nconst ObjProto = Object.prototype\nconst toString = ObjProto.toString\nexport const hasOwn = ObjProto.hasOwnProperty\n\nconst FN_MATCH_REGEXP = /^\\s*function (\\w+)/\n\n// https://github.com/vuejs/vue/blob/dev/src/core/util/props.js#L177\nexport function getType(\n  fn: VueProp<any> | (() => any) | (new (...args: any[]) => any),\n): string {\n  const type = (fn as VueProp<any>)?.type ?? fn\n  if (type) {\n    const match = type.toString().match(FN_MATCH_REGEXP)\n    return match ? match[1] : ''\n  }\n  return ''\n}\n\nexport function getNativeType(value: any): string {\n  if (value === null || value === undefined) return ''\n  const match = value.constructor.toString().match(FN_MATCH_REGEXP)\n  return match ? match[1].replace(/^Async/, '') : ''\n}\n\nexport function deepClone<T>(input: T): T {\n  if ('structuredClone' in globalThis) {\n    return structuredClone(input)\n  }\n  if (Array.isArray(input)) {\n    return [...input] as T\n  }\n  if (isPlainObject(input)) {\n    return Object.assign({}, input)\n  }\n  return input\n}\n\n/**\n * No-op function\n */\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nexport function noop() {}\n\n/**\n * A function that returns its first argument\n *\n * @param arg - Any argument\n */\nexport const identity = (arg: any) => arg\n\nlet warn: (msg: string, level?: VueTypesConfig['logLevel']) => void = noop\n\nif (process.env.NODE_ENV !== 'production') {\n  const hasConsole = typeof console !== 'undefined'\n  warn = hasConsole\n    ? function warn(msg: string, level = config.logLevel) {\n        if (config.silent === false) {\n          console[level](`[VueTypes warn]: ${msg}`)\n        }\n      }\n    : noop\n}\n\nexport { warn }\n\n/**\n * Checks for a own property in an object\n *\n * @param {object} obj - Object\n * @param {string} prop - Property to check\n */\nexport const has = <T, U extends keyof T>(obj: T, prop: U) =>\n  hasOwn.call(obj, prop)\n\n/**\n * Determines whether the passed value is an integer. Uses `Number.isInteger` if available\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/isInteger\n * @param {*} value - The value to be tested for being an integer.\n * @returns {boolean}\n */\nexport const isInteger =\n  Number.isInteger ||\n  function isInteger(value: unknown): value is number {\n    return (\n      typeof value === 'number' &&\n      isFinite(value) &&\n      Math.floor(value) === value\n    )\n  }\n\n/**\n * Determines whether the passed value is an Array.\n *\n * @param {*} value - The value to be tested for being an array.\n * @returns {boolean}\n */\nexport const isArray =\n  Array.isArray ||\n  function isArray(value): value is any[] {\n    return toString.call(value) === '[object Array]'\n  }\n\n/**\n * Checks if a value is a function\n *\n * @param {any} value - Value to check\n * @returns {boolean}\n */\n// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nexport const isFunction = <T extends Function>(value: unknown): value is T =>\n  toString.call(value) === '[object Function]'\n\n/**\n * Checks if the passed-in value is a VueTypes type\n * @param value - The value to check\n * @param name - Optional validator name\n */\nexport const isVueTypeDef = <T>(\n  value: any,\n  name?: string,\n): value is VueTypeDef<T> | VueTypeValidableDef<T> =>\n  isPlainObject(value) &&\n  has(value, '_vueTypes_name') &&\n  (!name || value._vueTypes_name === name)\n\n/**\n * Checks if the passed-in value is a Vue prop definition object or a VueTypes type\n * @param value - The value to check\n */\nexport const isComplexType = <T>(value: any): value is VueProp<T> =>\n  isPlainObject(value) &&\n  (has(value, 'type') ||\n    ['_vueTypes_name', 'validator', 'default', 'required'].some((k) =>\n      has(value, k),\n    ))\n\nexport interface WrappedFn {\n  (...args: any[]): any\n  __original: (...args: any[]) => any\n}\n\n/**\n * Binds a function to a context and saves a reference to the original.\n *\n * @param fn - Target function\n * @param ctx - New function context\n */\nexport function bindTo(fn: (...args: any[]) => any, ctx: any): WrappedFn {\n  return Object.defineProperty(fn.bind(ctx) as WrappedFn, '__original', {\n    value: fn,\n  })\n}\n\n/**\n * Returns the original function bounded with `bindTo`. If the passed-in function\n * has not be bound, the function itself will be returned instead.\n *\n * @param fn - Function to unwrap\n */\n// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nexport function unwrap<T extends WrappedFn | Function>(fn: T) {\n  return (fn as WrappedFn).__original ?? fn\n}\n\n/**\n * Validates a given value against a prop type object.\n *\n * If `silent` is `false` (default) will return a boolean. If it is set to `true`\n * it will return `true` on success or a string error message on failure\n *\n * @param {Object|*} type - Type to use for validation. Either a type object or a constructor\n * @param {*} value - Value to check\n * @param {boolean} silent - Silence warnings\n */\nexport function validateType<T, U>(\n  type: T,\n  value: U,\n  silent = false,\n): string | boolean {\n  let typeToCheck: Record<string, any>\n  let valid = true\n  let expectedType = ''\n  if (!isPlainObject(type)) {\n    typeToCheck = { type }\n  } else {\n    typeToCheck = type\n  }\n  const namePrefix = isVueTypeDef(typeToCheck)\n    ? typeToCheck._vueTypes_name + ' - '\n    : ''\n\n  if (isComplexType(typeToCheck) && typeToCheck.type !== null) {\n    if (typeToCheck.type === undefined || typeToCheck.type === true) {\n      return valid\n    }\n    if (!typeToCheck.required && value == null) {\n      return valid\n    }\n    if (isArray(typeToCheck.type)) {\n      valid = typeToCheck.type.some(\n        (type: any) => validateType(type, value, true) === true,\n      )\n      expectedType = typeToCheck.type\n        .map((type: any) => getType(type))\n        .join(' or ')\n    } else {\n      expectedType = getType(typeToCheck)\n\n      if (expectedType === 'Array') {\n        valid = isArray(value)\n      } else if (expectedType === 'Object') {\n        valid = isPlainObject(value)\n      } else if (\n        expectedType === 'String' ||\n        expectedType === 'Number' ||\n        expectedType === 'Boolean' ||\n        expectedType === 'Function'\n      ) {\n        valid = getNativeType(value) === expectedType\n      } else {\n        valid = value instanceof typeToCheck.type\n      }\n    }\n  }\n\n  if (!valid) {\n    const msg = `${namePrefix}value \"${value}\" should be of type \"${expectedType}\"`\n    if (silent === false) {\n      warn(msg)\n      return false\n    }\n    return msg\n  }\n\n  if (has(typeToCheck, 'validator') && isFunction(typeToCheck.validator)) {\n    const oldWarn = warn\n    const warnLog: string[] = []\n    warn = (msg) => {\n      warnLog.push(msg)\n    }\n\n    valid = typeToCheck.validator(value)\n    warn = oldWarn\n\n    if (!valid) {\n      const msg = (warnLog.length > 1 ? '* ' : '') + warnLog.join('\\n* ')\n      warnLog.length = 0\n      if (silent === false) {\n        warn(msg)\n        return valid\n      }\n      return msg\n    }\n  }\n  return valid\n}\n\n/**\n * Adds `isRequired` and `def` modifiers to an object\n *\n * @param {string} name - Type internal name\n * @param {object} obj - Object to enhance\n */\nexport function toType<T = any>(name: string, obj: PropOptions<T>) {\n  const type: VueTypeDef<T> = Object.defineProperties(obj as VueTypeDef<T>, {\n    _vueTypes_name: {\n      value: name,\n      writable: true,\n    },\n    isRequired: {\n      get() {\n        this.required = true\n        return this\n      },\n    },\n    def: {\n      value(def?: any) {\n        if (def === undefined) {\n          if (\n            this.type === Boolean ||\n            (Array.isArray(this.type) && this.type.includes(Boolean))\n          ) {\n            this.default = undefined\n            return\n          }\n          if (has(this, 'default')) {\n            delete this.default\n          }\n          return this\n        }\n        if (!isFunction(def) && validateType(this, def, true) !== true) {\n          warn(`${this._vueTypes_name} - invalid default value: \"${def}\"`)\n          return this\n        }\n        if (isArray(def)) {\n          this.default = () => deepClone(def)\n        } else if (isPlainObject(def)) {\n          this.default = () => deepClone(def)\n        } else {\n          this.default = def\n        }\n        return this\n      },\n    },\n  })\n\n  const { validator } = type\n  if (isFunction(validator)) {\n    type.validator = bindTo(validator, type)\n  }\n\n  return type\n}\n\n/**\n * Like `toType` but also adds the `validate()` method to the type object\n *\n * @param {string} name - Type internal name\n * @param {object} obj - Object to enhance\n */\nexport function toValidableType<T = any>(name: string, obj: PropOptions<T>) {\n  const type = toType<T>(name, obj)\n  return Object.defineProperty(type, 'validate', {\n    value(fn: ValidatorFunction<T>) {\n      if (isFunction(this.validator)) {\n        warn(\n          `${\n            this._vueTypes_name\n          } - calling .validate() will overwrite the current custom validator function. Validator info:\\n${JSON.stringify(\n            this,\n          )}`,\n        )\n      }\n      this.validator = bindTo(fn, this)\n      return this\n    },\n  }) as VueTypeValidableDef<T>\n}\n\n/**\n *  Clones an object preserving all of it's own keys.\n *\n * @param obj - Object to clone\n */\n\nexport function clone<T extends object>(obj: T): T {\n  const descriptors = {} as { [P in keyof T]: any }\n  Object.getOwnPropertyNames(obj).forEach((key) => {\n    descriptors[key as keyof T] = Object.getOwnPropertyDescriptor(obj, key)\n  })\n  return Object.defineProperties({}, descriptors) as T\n}\n\n/**\n * Return a new VueTypes type using another type as base.\n *\n * Properties in the `props` object will overwrite those defined in the source one\n * expect for the `validator` function. In that case both functions will be executed in series.\n *\n * @param name - Name of the new type\n * @param source - Source type\n * @param props - Custom type properties\n */\nexport function fromType<T extends VueTypeDef<any>>(name: string, source: T): T\nexport function fromType<\n  T extends VueTypeDef<any>,\n  V extends PropOptions<InferType<T>>,\n>(name: string, source: T, props: V): Omit<T, keyof V> & V\nexport function fromType<\n  T extends VueTypeDef<any>,\n  V extends PropOptions<InferType<T>>,\n>(name: string, source: T, props?: V) {\n  // 1. create an exact copy of the source type\n  const copy = clone(source)\n\n  // 2. give it a new name\n  copy._vueTypes_name = name\n\n  if (!isPlainObject(props)) {\n    return copy\n  }\n  const { validator, ...rest } = props\n\n  // 3. compose the validator function\n  // with the one on the source (if present)\n  // and ensure it is bound to the copy\n  if (isFunction(validator)) {\n    let { validator: prevValidator } = copy\n\n    if (prevValidator) {\n      prevValidator = unwrap(prevValidator) as (_v: any) => boolean\n    }\n\n    copy.validator = bindTo(\n      prevValidator\n        ? function (this: T, value: any, props: any) {\n            return (\n              // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n              prevValidator!.call(this, value, props) &&\n              validator.call(this, value, props)\n            )\n          }\n        : validator,\n      copy,\n    )\n  }\n  // 4. overwrite the rest, if present\n  return Object.assign(copy, rest as V)\n}\n\nexport function indent(string: string) {\n  return string.replace(/^(?!\\s*$)/gm, '  ')\n}\n", "import { toType, toValidableType, isInteger, warn } from '../utils'\nimport { PropOptions, PropType } from '../types'\n\nexport const any = <T = any>() => toValidableType<T>('any', {})\n\nexport const func = <T extends (...args: any[]) => any>() =>\n  toValidableType<T>('function', {\n    type: Function as PropType<T>,\n  })\n\nexport const bool = () =>\n  toValidableType('boolean', {\n    type: Boolean,\n  })\n\nexport const string = <T extends string = string>() =>\n  toValidableType<T>('string', {\n    type: String as unknown as PropType<T>,\n  })\n\nexport const number = <T extends number = number>() =>\n  toValidableType<T>('number', {\n    type: Number as unknown as PropType<T>,\n  })\n\nexport const array = <T>() =>\n  toValidableType<T[]>('array', {\n    type: Array,\n  })\n\nexport const object = <T extends Record<string, any>>() =>\n  toValidableType<T>('object', {\n    type: Object,\n  })\n\nexport const integer = <T extends number = number>() =>\n  toType<T>('integer', {\n    type: Number as unknown as PropType<T>,\n    validator(value) {\n      const res = isInteger(value)\n      if (res === false) {\n        warn(`integer - \"${value}\" is not an integer`)\n      }\n      return res\n    },\n  })\n\nexport const symbol = () =>\n  toType<symbol>('symbol', {\n    validator(value: unknown) {\n      const res = typeof value === 'symbol'\n      if (res === false) {\n        warn(`symbol - invalid value \"${value}\"`)\n      }\n      return res\n    },\n  })\n\nexport const nullable = () =>\n  Object.defineProperty(\n    {\n      type: null as unknown as PropType<null>,\n      validator(value: unknown) {\n        const res = value === null\n        if (res === false) {\n          warn(`nullable - value should be null`)\n        }\n        return res\n      },\n    },\n    '_vueTypes_name',\n    { value: 'nullable' },\n  ) as PropOptions<null>\n", "import { toType, warn } from '../utils'\nimport { ValidatorFunction, VueTypeDef, PropType } from '../types'\n\nexport default function custom<T>(\n  validatorFn: ValidatorFunction<T>,\n  warnMsg = 'custom validation failed',\n) {\n  if (typeof validatorFn !== 'function') {\n    throw new TypeError(\n      '[VueTypes error]: You must provide a function as argument',\n    )\n  }\n\n  return toType<T>(validatorFn.name || '<<anonymous function>>', {\n    type: null as unknown as PropType<T>,\n    validator(this: VueTypeDef<T>, value: T) {\n      const valid = validatorFn(value)\n      if (!valid) warn(`${this._vueTypes_name} - ${warnMsg}`)\n      return valid\n    },\n  })\n}\n", "import { Prop, PropOptions } from '../types'\nimport { toType, warn, isArray } from '../utils'\n\nexport default function oneOf<D, T extends readonly D[] = readonly D[]>(\n  arr: T,\n) {\n  if (!isArray(arr)) {\n    throw new TypeError(\n      '[VueTypes error]: You must provide an array as argument.',\n    )\n  }\n  const msg = `oneOf - value should be one of \"${arr\n    .map((v: any) => (typeof v === 'symbol' ? v.toString() : v))\n    .join('\", \"')}\".`\n  const base: PropOptions<T[number]> = {\n    validator(value) {\n      const valid = arr.indexOf(value) !== -1\n      if (!valid) warn(msg)\n      return valid\n    },\n  }\n  if (arr.indexOf(null) === -1) {\n    const type = arr.reduce(\n      (ret, v) => {\n        if (v !== null && v !== undefined) {\n          const constr = (v as any).constructor\n          // eslint-disable-next-line @typescript-eslint/no-unused-expressions\n          ret.indexOf(constr) === -1 && ret.push(constr)\n        }\n        return ret\n      },\n      [] as Prop<T[number]>[],\n    )\n\n    if (type.length > 0) {\n      base.type = type\n    }\n  }\n\n  return toType<T[number]>('oneOf', base)\n}\n", "import { Prop, VueProp, InferType, PropType } from '../types'\nimport {\n  isArray,\n  isComplexType,\n  isVueTypeDef,\n  isFunction,\n  toType,\n  validateType,\n  warn,\n  indent,\n} from '../utils'\n\nexport default function oneOfType<\n  D extends V,\n  U extends VueProp<any> | Prop<any> = any,\n  V = InferType<U>,\n>(arr: U[]) {\n  if (!isArray(arr)) {\n    throw new TypeError(\n      '[VueTypes error]: You must provide an array as argument',\n    )\n  }\n\n  let hasCustomValidators = false\n  let hasNullable = false\n\n  let nativeChecks: (Prop<V> | null)[] = []\n\n  // eslint-disable-next-line @typescript-eslint/prefer-for-of\n  for (let i = 0; i < arr.length; i += 1) {\n    const type = arr[i]\n    if (isComplexType<V>(type)) {\n      if (isFunction(type.validator)) {\n        hasCustomValidators = true\n      }\n      if (isVueTypeDef<V>(type, 'oneOf') && type.type) {\n        nativeChecks = nativeChecks.concat(type.type as PropType<V>)\n        continue\n      }\n      if (isVueTypeDef<V>(type, 'nullable')) {\n        hasNullable = true\n        continue\n      }\n      if (type.type === true || !type.type) {\n        warn('oneOfType - invalid usage of \"true\" and \"null\" as types.')\n        continue\n      }\n      nativeChecks = nativeChecks.concat(type.type)\n    } else {\n      nativeChecks.push(type as Prop<V>)\n    }\n  }\n\n  // filter duplicates\n  nativeChecks = nativeChecks.filter((t, i) => nativeChecks.indexOf(t) === i)\n\n  const typeProp =\n    hasNullable === false && nativeChecks.length > 0 ? nativeChecks : null\n\n  if (!hasCustomValidators) {\n    // we got just native objects (ie: Array, Object)\n    // delegate to Vue native prop check\n    return toType<D>('oneOfType', {\n      type: typeProp as unknown as PropType<D>,\n    })\n  }\n\n  return toType<D>('oneOfType', {\n    type: typeProp as unknown as PropType<D>,\n    validator(value) {\n      const err: string[] = []\n      const valid = arr.some((type) => {\n        const res = validateType(type, value, true)\n        if (typeof res === 'string') {\n          err.push(res)\n        }\n        return res === true\n      })\n\n      if (!valid) {\n        warn(\n          `oneOfType - provided value does not match any of the ${\n            err.length\n          } passed-in validators:\\n${indent(err.join('\\n'))}`,\n        )\n      }\n\n      return valid\n    },\n  })\n}\n", "import { Prop, VueProp, InferType } from '../types'\nimport { toType, validateType, warn, indent } from '../utils'\n\nexport default function arrayOf<T extends VueProp<any> | Prop<any>>(type: T) {\n  return toType<InferType<T>[]>('arrayOf', {\n    type: Array,\n    validator(values: any[]) {\n      let vResult: string | boolean = ''\n      const valid = values.every((value) => {\n        vResult = validateType(type, value, true)\n        return vResult === true\n      })\n      if (!valid) {\n        warn(`arrayOf - value validation error:\\n${indent(vResult as string)}`)\n      }\n      return valid\n    },\n  })\n}\n", "import { toType } from '../utils'\nimport { Constructor } from '../types'\n\nexport default function instanceOf<C extends Constructor>(\n  instanceConstructor: C,\n) {\n  return toType<InstanceType<C>>('instanceOf', {\n    type: instanceConstructor,\n  })\n}\n", "import { Prop, VueProp, InferType } from '../types'\nimport { toType, validateType, warn, indent, isPlainObject } from '../utils'\n\nexport default function objectOf<T extends VueProp<any> | Prop<any>>(type: T) {\n  return toType<Record<string, InferType<T>>>('objectOf', {\n    type: Object,\n    validator(obj) {\n      let vResult: string | boolean = ''\n      if (!isPlainObject(obj)) {\n        return false\n      }\n      const valid = Object.keys(obj).every((key) => {\n        vResult = validateType(type, obj[key], true)\n        return vResult === true\n      })\n\n      if (!valid) {\n        warn(`objectOf - value validation error:\\n${indent(vResult as string)}`)\n      }\n      return valid\n    },\n  })\n}\n", "import { <PERSON>p, VueProp, VueType<PERSON>ha<PERSON>, VueTypeLooseShape } from '../types'\nimport { toType, validateType, warn, isPlainObject, indent } from '../utils'\n\nexport default function shape<T extends object>(obj: {\n  [K in keyof T]: Prop<T[K]> | VueProp<T[K]>\n}): VueTypeShape<T> {\n  const keys = Object.keys(obj)\n  const requiredKeys = keys.filter((key) => !!(obj as any)[key]?.required)\n\n  const type = toType('shape', {\n    type: Object,\n    validator(this: VueTypeShape<T> | VueTypeLooseShape<T>, value) {\n      if (!isPlainObject(value)) {\n        return false\n      }\n      const valueKeys = Object.keys(value)\n\n      // check for required keys (if any)\n      if (\n        requiredKeys.length > 0 &&\n        requiredKeys.some((req) => valueKeys.indexOf(req) === -1)\n      ) {\n        const missing = requiredKeys.filter(\n          (req) => valueKeys.indexOf(req) === -1,\n        )\n        if (missing.length === 1) {\n          warn(`shape - required property \"${missing[0]}\" is not defined.`)\n        } else {\n          warn(\n            `shape - required properties \"${missing.join(\n              '\", \"',\n            )}\" are not defined.`,\n          )\n        }\n\n        return false\n      }\n\n      return valueKeys.every((key) => {\n        if (keys.indexOf(key) === -1) {\n          if ((this as VueTypeLooseShape<T>)._vueTypes_isLoose === true)\n            return true\n          warn(\n            `shape - shape definition does not include a \"${key}\" property. Allowed keys: \"${keys.join(\n              '\", \"',\n            )}\".`,\n          )\n          return false\n        }\n        const type = (obj as any)[key]\n        const valid = validateType(type, value[key], true)\n        if (typeof valid === 'string') {\n          warn(`shape - \"${key}\" property validation error:\\n ${indent(valid)}`)\n        }\n        return valid === true\n      })\n    },\n  }) as VueTypeShape<T>\n\n  Object.defineProperty(type, '_vueTypes_isLoose', {\n    writable: true,\n    value: false,\n  })\n\n  Object.defineProperty(type, 'loose', {\n    get() {\n      this._vueTypes_isLoose = true\n      return this\n    },\n  })\n\n  return type\n}\n", "import { toType, toValidableType, validateType, fromType, warn } from './utils'\n\nimport {\n  VueTypesDefaults,\n  VueTypeDef,\n  VueTypeValidableDef,\n  VueTypeShape,\n  VueTypeLooseShape,\n} from './types'\nimport { typeDefaults } from './sensibles'\nimport { PropOptions } from './types'\n\nimport {\n  any,\n  func,\n  bool,\n  string,\n  number,\n  array,\n  integer,\n  symbol,\n  object,\n  nullable,\n} from './validators/native'\nimport custom from './validators/custom'\nimport oneOf from './validators/oneof'\nimport oneOfType from './validators/oneoftype'\nimport arrayOf from './validators/arrayof'\nimport instanceOf from './validators/instanceof'\nimport objectOf from './validators/objectof'\nimport shape from './validators/shape'\nimport { config } from './config'\n\nconst BaseVueTypes = /*#__PURE__*/ (() =>\n  // eslint-disable-next-line @typescript-eslint/no-extraneous-class\n  class BaseVueTypes {\n    static defaults: Partial<VueTypesDefaults> = {}\n\n    static sensibleDefaults: Partial<VueTypesDefaults> | boolean\n\n    static config = config\n\n    static get any() {\n      return any()\n    }\n    static get func() {\n      return func().def(this.defaults.func)\n    }\n    static get bool() {\n      // prevent undefined to be explicitly set\n      if (this.defaults.bool === undefined) {\n        return bool()\n      }\n      return bool().def(this.defaults.bool)\n    }\n    static get string() {\n      return string().def(this.defaults.string)\n    }\n    static get number() {\n      return number().def(this.defaults.number)\n    }\n    static get array() {\n      return array().def(this.defaults.array)\n    }\n    static get object() {\n      return object().def(this.defaults.object)\n    }\n    static get integer() {\n      return integer().def(this.defaults.integer)\n    }\n    static get symbol() {\n      return symbol()\n    }\n\n    static get nullable() {\n      return nullable()\n    }\n\n    static readonly custom = custom\n    static readonly oneOf = oneOf\n    static readonly instanceOf = instanceOf\n    static readonly oneOfType = oneOfType\n    static readonly arrayOf = arrayOf\n    static readonly objectOf = objectOf\n    static readonly shape = shape\n\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    static extend(...args: any[]) {\n      warn(\n        `VueTypes.extend has been removed. Use the ES6+ method instead. See https://dwightjack.github.io/vue-types/advanced/extending-vue-types.html#extending-namespaced-validators-in-es6 for details.`,\n      )\n    }\n\n    static utils = {\n      validate<T, U>(value: T, type: U) {\n        return validateType<U, T>(type, value, true) === true\n      },\n      toType<T = unknown, Validable extends boolean = false>(\n        name: string,\n        obj: PropOptions<T>,\n        validable: Validable = false as Validable,\n      ): Validable extends true ? VueTypeValidableDef<T> : VueTypeDef<T> {\n        return (\n          validable ? toValidableType<T>(name, obj) : toType<T>(name, obj)\n        ) as any\n      },\n    }\n  })()\n\nfunction createTypes(defs: Partial<VueTypesDefaults> = typeDefaults()) {\n  return class extends BaseVueTypes {\n    static defaults: Partial<VueTypesDefaults> = { ...defs }\n\n    static get sensibleDefaults() {\n      return { ...this.defaults }\n    }\n\n    static set sensibleDefaults(v: boolean | Partial<VueTypesDefaults>) {\n      if (v === false) {\n        this.defaults = {}\n        return\n      }\n      if (v === true) {\n        this.defaults = { ...defs }\n        return\n      }\n      this.defaults = { ...v }\n    }\n  }\n}\n\nexport default class VueTypes /*#__PURE__*/ extends createTypes() {}\n\nexport {\n  any,\n  func,\n  bool,\n  string,\n  number,\n  array,\n  integer,\n  symbol,\n  object,\n  custom,\n  oneOf,\n  oneOfType,\n  arrayOf,\n  instanceOf,\n  objectOf,\n  shape,\n  nullable,\n  createTypes,\n  toType,\n  toValidableType,\n  validateType,\n  fromType,\n  config,\n}\n\nexport type VueTypesInterface = ReturnType<typeof createTypes>\nexport type { VueTypeDef, VueTypeValidableDef, VueTypeShape, VueTypeLooseShape }\n"], "names": ["warn", "isInteger", "isArray", "type", "props"], "mappings": ";;AAeA,MAAM,WAAW,MAAO,CAAA,SAAA,CAAA;AACxB,MAAM,WAAW,QAAS,CAAA,QAAA,CAAA;AACnB,MAAM,SAAS,QAAS,CAAA,cAAA,CAAA;AAE/B,MAAM,eAAkB,GAAA,oBAAA,CAAA;AAGjB,SAAS,QACd,EACQ,EAAA;AACR,EAAM,MAAA,IAAA,GAAQ,IAAqB,IAAQ,IAAA,EAAA,CAAA;AAC3C,EAAA,IAAI,IAAM,EAAA;AACR,IAAA,MAAM,KAAQ,GAAA,IAAA,CAAK,QAAS,EAAA,CAAE,MAAM,eAAe,CAAA,CAAA;AACnD,IAAO,OAAA,KAAA,GAAQ,KAAM,CAAA,CAAC,CAAI,GAAA,EAAA,CAAA;AAAA,GAC5B;AACA,EAAO,OAAA,EAAA,CAAA;AACT,CAAA;AAEO,SAAS,cAAc,KAAoB,EAAA;AAChD,EAAI,IAAA,KAAA,KAAU,QAAQ,KAAU,KAAA,KAAA,CAAA;AAAW,IAAO,OAAA,EAAA,CAAA;AAClD,EAAA,MAAM,QAAQ,KAAM,CAAA,WAAA,CAAY,QAAS,EAAA,CAAE,MAAM,eAAe,CAAA,CAAA;AAChE,EAAA,OAAO,QAAQ,KAAM,CAAA,CAAC,EAAE,OAAQ,CAAA,QAAA,EAAU,EAAE,CAAI,GAAA,EAAA,CAAA;AAClD,CAAA;AAEO,SAAS,UAAa,KAAa,EAAA;AACxC,EAAA,IAAI,qBAAqB,UAAY,EAAA;AACnC,IAAA,OAAO,gBAAgB,KAAK,CAAA,CAAA;AAAA,GAC9B;AACA,EAAI,IAAA,KAAA,CAAM,OAAQ,CAAA,KAAK,CAAG,EAAA;AACxB,IAAO,OAAA,CAAC,GAAG,KAAK,CAAA,CAAA;AAAA,GAClB;AACA,EAAI,IAAA,aAAA,CAAc,KAAK,CAAG,EAAA;AACxB,IAAA,OAAO,MAAO,CAAA,MAAA,CAAO,EAAC,EAAG,KAAK,CAAA,CAAA;AAAA,GAChC;AACA,EAAO,OAAA,KAAA,CAAA;AACT,CAAA;AAMO,SAAS,IAAO,GAAA;AAAC,CAAA;AASxB,IAAI,IAAkE,GAAA,IAAA,CAAA;AAEtE,IAAI,OAAA,CAAQ,GAAI,CAAA,QAAA,KAAa,YAAc,EAAA;AACzC,EAAM,MAAA,UAAA,GAAa,OAAO,OAAY,KAAA,WAAA,CAAA;AACtC,EAAA,IAAA,GAAO,aACH,SAASA,KAAAA,CAAK,GAAa,EAAA,KAAA,GAAQ,OAAO,QAAU,EAAA;AAClD,IAAI,IAAA,MAAA,CAAO,WAAW,KAAO,EAAA;AAC3B,MAAA,OAAA,CAAQ,KAAK,CAAA,CAAE,CAAoB,iBAAA,EAAA,GAAG,CAAE,CAAA,CAAA,CAAA;AAAA,KAC1C;AAAA,GAEF,GAAA,IAAA,CAAA;AACN,CAAA;AAUO,MAAM,MAAM,CAAuB,GAAA,EAAQ,SAChD,MAAO,CAAA,IAAA,CAAK,KAAK,IAAI,CAAA,CAAA;AAShB,MAAM,SACX,GAAA,MAAA,CAAO,SACP,IAAA,SAASC,WAAU,KAAiC,EAAA;AAClD,EACE,OAAA,OAAO,UAAU,QACjB,IAAA,QAAA,CAAS,KAAK,CACd,IAAA,IAAA,CAAK,KAAM,CAAA,KAAK,CAAM,KAAA,KAAA,CAAA;AAE1B,CAAA,CAAA;AAQK,MAAM,OACX,GAAA,KAAA,CAAM,OACN,IAAA,SAASC,SAAQ,KAAuB,EAAA;AACtC,EAAO,OAAA,QAAA,CAAS,IAAK,CAAA,KAAK,CAAM,KAAA,gBAAA,CAAA;AAClC,CAAA,CAAA;AASK,MAAM,aAAa,CAAqB,KAAA,KAC7C,QAAS,CAAA,IAAA,CAAK,KAAK,CAAM,KAAA,mBAAA,CAAA;AAOpB,MAAM,YAAe,GAAA,CAC1B,KACA,EAAA,IAAA,KAEA,cAAc,KAAK,CAAA,IACnB,GAAI,CAAA,KAAA,EAAO,gBAAgB,CAAA,KAC1B,CAAC,IAAA,IAAQ,MAAM,cAAmB,KAAA,IAAA,CAAA,CAAA;AAM9B,MAAM,aAAgB,GAAA,CAAI,KAC/B,KAAA,aAAA,CAAc,KAAK,CAClB,KAAA,GAAA,CAAI,KAAO,EAAA,MAAM,KAChB,CAAC,gBAAA,EAAkB,WAAa,EAAA,SAAA,EAAW,UAAU,CAAE,CAAA,IAAA;AAAA,EAAK,CAAC,CAAA,KAC3D,GAAI,CAAA,KAAA,EAAO,CAAC,CAAA;AACd,CAAA,CAAA,CAAA;AAaY,SAAA,MAAA,CAAO,IAA6B,GAAqB,EAAA;AACvE,EAAA,OAAO,OAAO,cAAe,CAAA,EAAA,CAAG,IAAK,CAAA,GAAG,GAAgB,YAAc,EAAA;AAAA,IACpE,KAAO,EAAA,EAAA;AAAA,GACR,CAAA,CAAA;AACH,CAAA;AASO,SAAS,OAAuC,EAAO,EAAA;AAC5D,EAAA,OAAQ,GAAiB,UAAc,IAAA,EAAA,CAAA;AACzC,CAAA;AAYO,SAAS,YACd,CAAA,IAAA,EACA,KACA,EAAA,MAAA,GAAS,KACS,EAAA;AAClB,EAAI,IAAA,WAAA,CAAA;AACJ,EAAA,IAAI,KAAQ,GAAA,IAAA,CAAA;AACZ,EAAA,IAAI,YAAe,GAAA,EAAA,CAAA;AACnB,EAAI,IAAA,CAAC,aAAc,CAAA,IAAI,CAAG,EAAA;AACxB,IAAA,WAAA,GAAc,EAAE,IAAK,EAAA,CAAA;AAAA,GAChB,MAAA;AACL,IAAc,WAAA,GAAA,IAAA,CAAA;AAAA,GAChB;AACA,EAAA,MAAM,aAAa,YAAa,CAAA,WAAW,CACvC,GAAA,WAAA,CAAY,iBAAiB,KAC7B,GAAA,EAAA,CAAA;AAEJ,EAAA,IAAI,aAAc,CAAA,WAAW,CAAK,IAAA,WAAA,CAAY,SAAS,IAAM,EAAA;AAC3D,IAAA,IAAI,WAAY,CAAA,IAAA,KAAS,KAAa,CAAA,IAAA,WAAA,CAAY,SAAS,IAAM,EAAA;AAC/D,MAAO,OAAA,KAAA,CAAA;AAAA,KACT;AACA,IAAA,IAAI,CAAC,WAAA,CAAY,QAAY,IAAA,KAAA,IAAS,IAAM,EAAA;AAC1C,MAAO,OAAA,KAAA,CAAA;AAAA,KACT;AACA,IAAI,IAAA,OAAA,CAAQ,WAAY,CAAA,IAAI,CAAG,EAAA;AAC7B,MAAA,KAAA,GAAQ,YAAY,IAAK,CAAA,IAAA;AAAA,QACvB,CAACC,KAAc,KAAA,YAAA,CAAaA,KAAM,EAAA,KAAA,EAAO,IAAI,CAAM,KAAA,IAAA;AAAA,OACrD,CAAA;AACA,MAAe,YAAA,GAAA,WAAA,CAAY,IACxB,CAAA,GAAA,CAAI,CAACA,KAAAA,KAAc,QAAQA,KAAI,CAAC,CAChC,CAAA,IAAA,CAAK,MAAM,CAAA,CAAA;AAAA,KACT,MAAA;AACL,MAAA,YAAA,GAAe,QAAQ,WAAW,CAAA,CAAA;AAElC,MAAA,IAAI,iBAAiB,OAAS,EAAA;AAC5B,QAAA,KAAA,GAAQ,QAAQ,KAAK,CAAA,CAAA;AAAA,OACvB,MAAA,IAAW,iBAAiB,QAAU,EAAA;AACpC,QAAA,KAAA,GAAQ,cAAc,KAAK,CAAA,CAAA;AAAA,OAC7B,MAAA,IACE,iBAAiB,QACjB,IAAA,YAAA,KAAiB,YACjB,YAAiB,KAAA,SAAA,IACjB,iBAAiB,UACjB,EAAA;AACA,QAAQ,KAAA,GAAA,aAAA,CAAc,KAAK,CAAM,KAAA,YAAA,CAAA;AAAA,OAC5B,MAAA;AACL,QAAA,KAAA,GAAQ,iBAAiB,WAAY,CAAA,IAAA,CAAA;AAAA,OACvC;AAAA,KACF;AAAA,GACF;AAEA,EAAA,IAAI,CAAC,KAAO,EAAA;AACV,IAAA,MAAM,MAAM,CAAG,EAAA,UAAU,CAAU,OAAA,EAAA,KAAK,wBAAwB,YAAY,CAAA,CAAA,CAAA,CAAA;AAC5E,IAAA,IAAI,WAAW,KAAO,EAAA;AACpB,MAAA,IAAA,CAAK,GAAG,CAAA,CAAA;AACR,MAAO,OAAA,KAAA,CAAA;AAAA,KACT;AACA,IAAO,OAAA,GAAA,CAAA;AAAA,GACT;AAEA,EAAA,IAAI,IAAI,WAAa,EAAA,WAAW,KAAK,UAAW,CAAA,WAAA,CAAY,SAAS,CAAG,EAAA;AACtE,IAAA,MAAM,OAAU,GAAA,IAAA,CAAA;AAChB,IAAA,MAAM,UAAoB,EAAC,CAAA;AAC3B,IAAA,IAAA,GAAO,CAAC,GAAQ,KAAA;AACd,MAAA,OAAA,CAAQ,KAAK,GAAG,CAAA,CAAA;AAAA,KAClB,CAAA;AAEA,IAAQ,KAAA,GAAA,WAAA,CAAY,UAAU,KAAK,CAAA,CAAA;AACnC,IAAO,IAAA,GAAA,OAAA,CAAA;AAEP,IAAA,IAAI,CAAC,KAAO,EAAA;AACV,MAAM,MAAA,GAAA,GAAA,CAAO,QAAQ,MAAS,GAAA,CAAA,GAAI,OAAO,EAAM,IAAA,OAAA,CAAQ,KAAK,MAAM,CAAA,CAAA;AAClE,MAAA,OAAA,CAAQ,MAAS,GAAA,CAAA,CAAA;AACjB,MAAA,IAAI,WAAW,KAAO,EAAA;AACpB,QAAA,IAAA,CAAK,GAAG,CAAA,CAAA;AACR,QAAO,OAAA,KAAA,CAAA;AAAA,OACT;AACA,MAAO,OAAA,GAAA,CAAA;AAAA,KACT;AAAA,GACF;AACA,EAAO,OAAA,KAAA,CAAA;AACT,CAAA;AAQgB,SAAA,MAAA,CAAgB,MAAc,GAAqB,EAAA;AACjE,EAAM,MAAA,IAAA,GAAsB,MAAO,CAAA,gBAAA,CAAiB,GAAsB,EAAA;AAAA,IACxE,cAAgB,EAAA;AAAA,MACd,KAAO,EAAA,IAAA;AAAA,MACP,QAAU,EAAA,IAAA;AAAA,KACZ;AAAA,IACA,UAAY,EAAA;AAAA,MACV,GAAM,GAAA;AACJ,QAAA,IAAA,CAAK,QAAW,GAAA,IAAA,CAAA;AAChB,QAAO,OAAA,IAAA,CAAA;AAAA,OACT;AAAA,KACF;AAAA,IACA,GAAK,EAAA;AAAA,MACH,MAAM,GAAW,EAAA;AACf,QAAA,IAAI,QAAQ,KAAW,CAAA,EAAA;AACrB,UAAA,IACE,IAAK,CAAA,IAAA,KAAS,OACb,IAAA,KAAA,CAAM,OAAQ,CAAA,IAAA,CAAK,IAAI,CAAA,IAAK,IAAK,CAAA,IAAA,CAAK,QAAS,CAAA,OAAO,CACvD,EAAA;AACA,YAAA,IAAA,CAAK,OAAU,GAAA,KAAA,CAAA,CAAA;AACf,YAAA,OAAA;AAAA,WACF;AACA,UAAI,IAAA,GAAA,CAAI,IAAM,EAAA,SAAS,CAAG,EAAA;AACxB,YAAA,OAAO,IAAK,CAAA,OAAA,CAAA;AAAA,WACd;AACA,UAAO,OAAA,IAAA,CAAA;AAAA,SACT;AACA,QAAI,IAAA,CAAC,WAAW,GAAG,CAAA,IAAK,aAAa,IAAM,EAAA,GAAA,EAAK,IAAI,CAAA,KAAM,IAAM,EAAA;AAC9D,UAAA,IAAA,CAAK,CAAG,EAAA,IAAA,CAAK,cAAc,CAAA,2BAAA,EAA8B,GAAG,CAAG,CAAA,CAAA,CAAA,CAAA;AAC/D,UAAO,OAAA,IAAA,CAAA;AAAA,SACT;AACA,QAAI,IAAA,OAAA,CAAQ,GAAG,CAAG,EAAA;AAChB,UAAK,IAAA,CAAA,OAAA,GAAU,MAAM,SAAA,CAAU,GAAG,CAAA,CAAA;AAAA,SACpC,MAAA,IAAW,aAAc,CAAA,GAAG,CAAG,EAAA;AAC7B,UAAK,IAAA,CAAA,OAAA,GAAU,MAAM,SAAA,CAAU,GAAG,CAAA,CAAA;AAAA,SAC7B,MAAA;AACL,UAAA,IAAA,CAAK,OAAU,GAAA,GAAA,CAAA;AAAA,SACjB;AACA,QAAO,OAAA,IAAA,CAAA;AAAA,OACT;AAAA,KACF;AAAA,GACD,CAAA,CAAA;AAED,EAAM,MAAA,EAAE,WAAc,GAAA,IAAA,CAAA;AACtB,EAAI,IAAA,UAAA,CAAW,SAAS,CAAG,EAAA;AACzB,IAAK,IAAA,CAAA,SAAA,GAAY,MAAO,CAAA,SAAA,EAAW,IAAI,CAAA,CAAA;AAAA,GACzC;AAEA,EAAO,OAAA,IAAA,CAAA;AACT,CAAA;AAQgB,SAAA,eAAA,CAAyB,MAAc,GAAqB,EAAA;AAC1E,EAAM,MAAA,IAAA,GAAO,MAAU,CAAA,IAAA,EAAM,GAAG,CAAA,CAAA;AAChC,EAAO,OAAA,MAAA,CAAO,cAAe,CAAA,IAAA,EAAM,UAAY,EAAA;AAAA,IAC7C,MAAM,EAA0B,EAAA;AAC9B,MAAI,IAAA,UAAA,CAAW,IAAK,CAAA,SAAS,CAAG,EAAA;AAC9B,QAAA,IAAA;AAAA,UACE,CAAA,EACE,KAAK,cACP,CAAA;AAAA,EAAiG,IAAK,CAAA,SAAA;AAAA,YACpG,IAAA;AAAA,WACD,CAAA,CAAA;AAAA,SACH,CAAA;AAAA,OACF;AACA,MAAK,IAAA,CAAA,SAAA,GAAY,MAAO,CAAA,EAAA,EAAI,IAAI,CAAA,CAAA;AAChC,MAAO,OAAA,IAAA,CAAA;AAAA,KACT;AAAA,GACD,CAAA,CAAA;AACH,CAAA;AAQO,SAAS,MAAwB,GAAW,EAAA;AACjD,EAAA,MAAM,cAAc,EAAC,CAAA;AACrB,EAAA,MAAA,CAAO,mBAAoB,CAAA,GAAG,CAAE,CAAA,OAAA,CAAQ,CAAC,GAAQ,KAAA;AAC/C,IAAA,WAAA,CAAY,GAAc,CAAA,GAAI,MAAO,CAAA,wBAAA,CAAyB,KAAK,GAAG,CAAA,CAAA;AAAA,GACvE,CAAA,CAAA;AACD,EAAA,OAAO,MAAO,CAAA,gBAAA,CAAiB,EAAC,EAAG,WAAW,CAAA,CAAA;AAChD,CAAA;AAiBgB,SAAA,QAAA,CAGd,IAAc,EAAA,MAAA,EAAW,KAAW,EAAA;AAEpC,EAAM,MAAA,IAAA,GAAO,MAAM,MAAM,CAAA,CAAA;AAGzB,EAAA,IAAA,CAAK,cAAiB,GAAA,IAAA,CAAA;AAEtB,EAAI,IAAA,CAAC,aAAc,CAAA,KAAK,CAAG,EAAA;AACzB,IAAO,OAAA,IAAA,CAAA;AAAA,GACT;AACA,EAAA,MAAM,EAAE,SAAA,EAAW,GAAG,IAAA,EAAS,GAAA,KAAA,CAAA;AAK/B,EAAI,IAAA,UAAA,CAAW,SAAS,CAAG,EAAA;AACzB,IAAI,IAAA,EAAE,SAAW,EAAA,aAAA,EAAkB,GAAA,IAAA,CAAA;AAEnC,IAAA,IAAI,aAAe,EAAA;AACjB,MAAA,aAAA,GAAgB,OAAO,aAAa,CAAA,CAAA;AAAA,KACtC;AAEA,IAAA,IAAA,CAAK,SAAY,GAAA,MAAA;AAAA,MACf,aAAA,GACI,SAAmB,KAAA,EAAYC,MAAY,EAAA;AACzC,QAAA;AAAA;AAAA,UAEE,aAAA,CAAe,IAAK,CAAA,IAAA,EAAM,KAAOA,EAAAA,MAAK,KACtC,SAAU,CAAA,IAAA,CAAK,IAAM,EAAA,KAAA,EAAOA,MAAK,CAAA;AAAA,UAAA;AAAA,OAGrC,GAAA,SAAA;AAAA,MACJ,IAAA;AAAA,KACF,CAAA;AAAA,GACF;AAEA,EAAO,OAAA,MAAA,CAAO,MAAO,CAAA,IAAA,EAAM,IAAS,CAAA,CAAA;AACtC,CAAA;AAEO,SAAS,OAAO,MAAgB,EAAA;AACrC,EAAO,OAAA,MAAA,CAAO,OAAQ,CAAA,aAAA,EAAe,IAAI,CAAA,CAAA;AAC3C;;ACzaO,MAAM,GAAM,GAAA,MAAe,eAAmB,CAAA,KAAA,EAAO,EAAE,EAAA;AAEjD,MAAA,IAAA,GAAO,MAClB,eAAA,CAAmB,UAAY,EAAA;AAAA,EAC7B,IAAM,EAAA,QAAA;AACR,CAAC,EAAA;AAEU,MAAA,IAAA,GAAO,MAClB,eAAA,CAAgB,SAAW,EAAA;AAAA,EACzB,IAAM,EAAA,OAAA;AACR,CAAC,EAAA;AAEU,MAAA,MAAA,GAAS,MACpB,eAAA,CAAmB,QAAU,EAAA;AAAA,EAC3B,IAAM,EAAA,MAAA;AACR,CAAC,EAAA;AAEU,MAAA,MAAA,GAAS,MACpB,eAAA,CAAmB,QAAU,EAAA;AAAA,EAC3B,IAAM,EAAA,MAAA;AACR,CAAC,EAAA;AAEU,MAAA,KAAA,GAAQ,MACnB,eAAA,CAAqB,OAAS,EAAA;AAAA,EAC5B,IAAM,EAAA,KAAA;AACR,CAAC,EAAA;AAEU,MAAA,MAAA,GAAS,MACpB,eAAA,CAAmB,QAAU,EAAA;AAAA,EAC3B,IAAM,EAAA,MAAA;AACR,CAAC,EAAA;AAEU,MAAA,OAAA,GAAU,MACrB,MAAA,CAAU,SAAW,EAAA;AAAA,EACnB,IAAM,EAAA,MAAA;AAAA,EACN,UAAU,KAAO,EAAA;AACf,IAAM,MAAA,GAAA,GAAM,UAAU,KAAK,CAAA,CAAA;AAC3B,IAAA,IAAI,QAAQ,KAAO,EAAA;AACjB,MAAK,IAAA,CAAA,CAAA,WAAA,EAAc,KAAK,CAAqB,mBAAA,CAAA,CAAA,CAAA;AAAA,KAC/C;AACA,IAAO,OAAA,GAAA,CAAA;AAAA,GACT;AACF,CAAC,EAAA;AAEU,MAAA,MAAA,GAAS,MACpB,MAAA,CAAe,QAAU,EAAA;AAAA,EACvB,UAAU,KAAgB,EAAA;AACxB,IAAM,MAAA,GAAA,GAAM,OAAO,KAAU,KAAA,QAAA,CAAA;AAC7B,IAAA,IAAI,QAAQ,KAAO,EAAA;AACjB,MAAK,IAAA,CAAA,CAAA,wBAAA,EAA2B,KAAK,CAAG,CAAA,CAAA,CAAA,CAAA;AAAA,KAC1C;AACA,IAAO,OAAA,GAAA,CAAA;AAAA,GACT;AACF,CAAC,EAAA;AAEU,MAAA,QAAA,GAAW,MACtB,MAAO,CAAA,cAAA;AAAA,EACL;AAAA,IACE,IAAM,EAAA,IAAA;AAAA,IACN,UAAU,KAAgB,EAAA;AACxB,MAAA,MAAM,MAAM,KAAU,KAAA,IAAA,CAAA;AACtB,MAAA,IAAI,QAAQ,KAAO,EAAA;AACjB,QAAA,IAAA,CAAK,CAAiC,+BAAA,CAAA,CAAA,CAAA;AAAA,OACxC;AACA,MAAO,OAAA,GAAA,CAAA;AAAA,KACT;AAAA,GACF;AAAA,EACA,gBAAA;AAAA,EACA,EAAE,OAAO,UAAW,EAAA;AACtB;;ACrEsB,SAAA,MAAA,CACtB,WACA,EAAA,OAAA,GAAU,0BACV,EAAA;AACA,EAAI,IAAA,OAAO,gBAAgB,UAAY,EAAA;AACrC,IAAA,MAAM,IAAI,SAAA;AAAA,MACR,2DAAA;AAAA,KACF,CAAA;AAAA,GACF;AAEA,EAAO,OAAA,MAAA,CAAU,WAAY,CAAA,IAAA,IAAQ,wBAA0B,EAAA;AAAA,IAC7D,IAAM,EAAA,IAAA;AAAA,IACN,UAA+B,KAAU,EAAA;AACvC,MAAM,MAAA,KAAA,GAAQ,YAAY,KAAK,CAAA,CAAA;AAC/B,MAAA,IAAI,CAAC,KAAA;AAAO,QAAA,IAAA,CAAK,CAAG,EAAA,IAAA,CAAK,cAAc,CAAA,GAAA,EAAM,OAAO,CAAE,CAAA,CAAA,CAAA;AACtD,MAAO,OAAA,KAAA,CAAA;AAAA,KACT;AAAA,GACD,CAAA,CAAA;AACH;;AClBA,SAAwB,MACtB,GACA,EAAA;AACA,EAAI,IAAA,CAAC,OAAQ,CAAA,GAAG,CAAG,EAAA;AACjB,IAAA,MAAM,IAAI,SAAA;AAAA,MACR,0DAAA;AAAA,KACF,CAAA;AAAA,GACF;AACA,EAAA,MAAM,MAAM,CAAmC,gCAAA,EAAA,GAAA,CAC5C,GAAI,CAAA,CAAC,MAAY,OAAO,CAAA,KAAM,QAAW,GAAA,CAAA,CAAE,UAAa,GAAA,CAAE,CAC1D,CAAA,IAAA,CAAK,MAAM,CAAC,CAAA,EAAA,CAAA,CAAA;AACf,EAAA,MAAM,IAA+B,GAAA;AAAA,IACnC,UAAU,KAAO,EAAA;AACf,MAAA,MAAM,KAAQ,GAAA,GAAA,CAAI,OAAQ,CAAA,KAAK,CAAM,KAAA,CAAA,CAAA,CAAA;AACrC,MAAA,IAAI,CAAC,KAAA;AAAO,QAAA,IAAA,CAAK,GAAG,CAAA,CAAA;AACpB,MAAO,OAAA,KAAA,CAAA;AAAA,KACT;AAAA,GACF,CAAA;AACA,EAAA,IAAI,GAAI,CAAA,OAAA,CAAQ,IAAI,CAAA,KAAM,CAAI,CAAA,EAAA;AAC5B,IAAA,MAAM,OAAO,GAAI,CAAA,MAAA;AAAA,MACf,CAAC,KAAK,CAAM,KAAA;AACV,QAAI,IAAA,CAAA,KAAM,IAAQ,IAAA,CAAA,KAAM,KAAW,CAAA,EAAA;AACjC,UAAA,MAAM,SAAU,CAAU,CAAA,WAAA,CAAA;AAE1B,UAAA,GAAA,CAAI,QAAQ,MAAM,CAAA,KAAM,CAAM,CAAA,IAAA,GAAA,CAAI,KAAK,MAAM,CAAA,CAAA;AAAA,SAC/C;AACA,QAAO,OAAA,GAAA,CAAA;AAAA,OACT;AAAA,MACA,EAAC;AAAA,KACH,CAAA;AAEA,IAAI,IAAA,IAAA,CAAK,SAAS,CAAG,EAAA;AACnB,MAAA,IAAA,CAAK,IAAO,GAAA,IAAA,CAAA;AAAA,KACd;AAAA,GACF;AAEA,EAAO,OAAA,MAAA,CAAkB,SAAS,IAAI,CAAA,CAAA;AACxC;;AC5BA,SAAwB,UAItB,GAAU,EAAA;AACV,EAAI,IAAA,CAAC,OAAQ,CAAA,GAAG,CAAG,EAAA;AACjB,IAAA,MAAM,IAAI,SAAA;AAAA,MACR,yDAAA;AAAA,KACF,CAAA;AAAA,GACF;AAEA,EAAA,IAAI,mBAAsB,GAAA,KAAA,CAAA;AAC1B,EAAA,IAAI,WAAc,GAAA,KAAA,CAAA;AAElB,EAAA,IAAI,eAAmC,EAAC,CAAA;AAGxC,EAAA,KAAA,IAAS,IAAI,CAAG,EAAA,CAAA,GAAI,GAAI,CAAA,MAAA,EAAQ,KAAK,CAAG,EAAA;AACtC,IAAM,MAAA,IAAA,GAAO,IAAI,CAAC,CAAA,CAAA;AAClB,IAAI,IAAA,aAAA,CAAiB,IAAI,CAAG,EAAA;AAC1B,MAAI,IAAA,UAAA,CAAW,IAAK,CAAA,SAAS,CAAG,EAAA;AAC9B,QAAsB,mBAAA,GAAA,IAAA,CAAA;AAAA,OACxB;AACA,MAAA,IAAI,YAAgB,CAAA,IAAA,EAAM,OAAO,CAAA,IAAK,KAAK,IAAM,EAAA;AAC/C,QAAe,YAAA,GAAA,YAAA,CAAa,MAAO,CAAA,IAAA,CAAK,IAAmB,CAAA,CAAA;AAC3D,QAAA,SAAA;AAAA,OACF;AACA,MAAI,IAAA,YAAA,CAAgB,IAAM,EAAA,UAAU,CAAG,EAAA;AACrC,QAAc,WAAA,GAAA,IAAA,CAAA;AACd,QAAA,SAAA;AAAA,OACF;AACA,MAAA,IAAI,IAAK,CAAA,IAAA,KAAS,IAAQ,IAAA,CAAC,KAAK,IAAM,EAAA;AACpC,QAAA,IAAA,CAAK,0DAA0D,CAAA,CAAA;AAC/D,QAAA,SAAA;AAAA,OACF;AACA,MAAe,YAAA,GAAA,YAAA,CAAa,MAAO,CAAA,IAAA,CAAK,IAAI,CAAA,CAAA;AAAA,KACvC,MAAA;AACL,MAAA,YAAA,CAAa,KAAK,IAAe,CAAA,CAAA;AAAA,KACnC;AAAA,GACF;AAGA,EAAe,YAAA,GAAA,YAAA,CAAa,OAAO,CAAC,CAAA,EAAG,MAAM,YAAa,CAAA,OAAA,CAAQ,CAAC,CAAA,KAAM,CAAC,CAAA,CAAA;AAE1E,EAAA,MAAM,WACJ,WAAgB,KAAA,KAAA,IAAS,YAAa,CAAA,MAAA,GAAS,IAAI,YAAe,GAAA,IAAA,CAAA;AAEpE,EAAA,IAAI,CAAC,mBAAqB,EAAA;AAGxB,IAAA,OAAO,OAAU,WAAa,EAAA;AAAA,MAC5B,IAAM,EAAA,QAAA;AAAA,KACP,CAAA,CAAA;AAAA,GACH;AAEA,EAAA,OAAO,OAAU,WAAa,EAAA;AAAA,IAC5B,IAAM,EAAA,QAAA;AAAA,IACN,UAAU,KAAO,EAAA;AACf,MAAA,MAAM,MAAgB,EAAC,CAAA;AACvB,MAAA,MAAM,KAAQ,GAAA,GAAA,CAAI,IAAK,CAAA,CAAC,IAAS,KAAA;AAC/B,QAAA,MAAM,GAAM,GAAA,YAAA,CAAa,IAAM,EAAA,KAAA,EAAO,IAAI,CAAA,CAAA;AAC1C,QAAI,IAAA,OAAO,QAAQ,QAAU,EAAA;AAC3B,UAAA,GAAA,CAAI,KAAK,GAAG,CAAA,CAAA;AAAA,SACd;AACA,QAAA,OAAO,GAAQ,KAAA,IAAA,CAAA;AAAA,OAChB,CAAA,CAAA;AAED,MAAA,IAAI,CAAC,KAAO,EAAA;AACV,QAAA,IAAA;AAAA,UACE,CAAA,qDAAA,EACE,IAAI,MACN,CAAA;AAAA,EAA2B,MAAO,CAAA,GAAA,CAAI,IAAK,CAAA,IAAI,CAAC,CAAC,CAAA,CAAA;AAAA,SACnD,CAAA;AAAA,OACF;AAEA,MAAO,OAAA,KAAA,CAAA;AAAA,KACT;AAAA,GACD,CAAA,CAAA;AACH;;ACvFA,SAAwB,QAA4C,IAAS,EAAA;AAC3E,EAAA,OAAO,OAAuB,SAAW,EAAA;AAAA,IACvC,IAAM,EAAA,KAAA;AAAA,IACN,UAAU,MAAe,EAAA;AACvB,MAAA,IAAI,OAA4B,GAAA,EAAA,CAAA;AAChC,MAAA,MAAM,KAAQ,GAAA,MAAA,CAAO,KAAM,CAAA,CAAC,KAAU,KAAA;AACpC,QAAU,OAAA,GAAA,YAAA,CAAa,IAAM,EAAA,KAAA,EAAO,IAAI,CAAA,CAAA;AACxC,QAAA,OAAO,OAAY,KAAA,IAAA,CAAA;AAAA,OACpB,CAAA,CAAA;AACD,MAAA,IAAI,CAAC,KAAO,EAAA;AACV,QAAK,IAAA,CAAA,CAAA;AAAA,EAAsC,MAAA,CAAO,OAAiB,CAAC,CAAE,CAAA,CAAA,CAAA;AAAA,OACxE;AACA,MAAO,OAAA,KAAA,CAAA;AAAA,KACT;AAAA,GACD,CAAA,CAAA;AACH;;ACfA,SAAwB,WACtB,mBACA,EAAA;AACA,EAAA,OAAO,OAAwB,YAAc,EAAA;AAAA,IAC3C,IAAM,EAAA,mBAAA;AAAA,GACP,CAAA,CAAA;AACH;;ACNA,SAAwB,SAA6C,IAAS,EAAA;AAC5E,EAAA,OAAO,OAAqC,UAAY,EAAA;AAAA,IACtD,IAAM,EAAA,MAAA;AAAA,IACN,UAAU,GAAK,EAAA;AACb,MAAA,IAAI,OAA4B,GAAA,EAAA,CAAA;AAChC,MAAI,IAAA,CAAC,aAAc,CAAA,GAAG,CAAG,EAAA;AACvB,QAAO,OAAA,KAAA,CAAA;AAAA,OACT;AACA,MAAA,MAAM,QAAQ,MAAO,CAAA,IAAA,CAAK,GAAG,CAAE,CAAA,KAAA,CAAM,CAAC,GAAQ,KAAA;AAC5C,QAAA,OAAA,GAAU,YAAa,CAAA,IAAA,EAAM,GAAI,CAAA,GAAG,GAAG,IAAI,CAAA,CAAA;AAC3C,QAAA,OAAO,OAAY,KAAA,IAAA,CAAA;AAAA,OACpB,CAAA,CAAA;AAED,MAAA,IAAI,CAAC,KAAO,EAAA;AACV,QAAK,IAAA,CAAA,CAAA;AAAA,EAAuC,MAAA,CAAO,OAAiB,CAAC,CAAE,CAAA,CAAA,CAAA;AAAA,OACzE;AACA,MAAO,OAAA,KAAA,CAAA;AAAA,KACT;AAAA,GACD,CAAA,CAAA;AACH;;ACnBA,SAAwB,MAAwB,GAE5B,EAAA;AAClB,EAAM,MAAA,IAAA,GAAO,MAAO,CAAA,IAAA,CAAK,GAAG,CAAA,CAAA;AAC5B,EAAM,MAAA,YAAA,GAAe,IAAK,CAAA,MAAA,CAAO,CAAC,GAAA,KAAQ,CAAC,CAAE,GAAA,CAAY,GAAG,CAAA,EAAG,QAAQ,CAAA,CAAA;AAEvE,EAAM,MAAA,IAAA,GAAO,OAAO,OAAS,EAAA;AAAA,IAC3B,IAAM,EAAA,MAAA;AAAA,IACN,UAAwD,KAAO,EAAA;AAC7D,MAAI,IAAA,CAAC,aAAc,CAAA,KAAK,CAAG,EAAA;AACzB,QAAO,OAAA,KAAA,CAAA;AAAA,OACT;AACA,MAAM,MAAA,SAAA,GAAY,MAAO,CAAA,IAAA,CAAK,KAAK,CAAA,CAAA;AAGnC,MAAA,IACE,YAAa,CAAA,MAAA,GAAS,CACtB,IAAA,YAAA,CAAa,IAAK,CAAA,CAAC,GAAQ,KAAA,SAAA,CAAU,OAAQ,CAAA,GAAG,CAAM,KAAA,CAAA,CAAE,CACxD,EAAA;AACA,QAAA,MAAM,UAAU,YAAa,CAAA,MAAA;AAAA,UAC3B,CAAC,GAAA,KAAQ,SAAU,CAAA,OAAA,CAAQ,GAAG,CAAM,KAAA,CAAA,CAAA;AAAA,SACtC,CAAA;AACA,QAAI,IAAA,OAAA,CAAQ,WAAW,CAAG,EAAA;AACxB,UAAA,IAAA,CAAK,CAA8B,2BAAA,EAAA,OAAA,CAAQ,CAAC,CAAC,CAAmB,iBAAA,CAAA,CAAA,CAAA;AAAA,SAC3D,MAAA;AACL,UAAA,IAAA;AAAA,YACE,gCAAgC,OAAQ,CAAA,IAAA;AAAA,cACtC,MAAA;AAAA,aACD,CAAA,kBAAA,CAAA;AAAA,WACH,CAAA;AAAA,SACF;AAEA,QAAO,OAAA,KAAA,CAAA;AAAA,OACT;AAEA,MAAO,OAAA,SAAA,CAAU,KAAM,CAAA,CAAC,GAAQ,KAAA;AAC9B,QAAA,IAAI,IAAK,CAAA,OAAA,CAAQ,GAAG,CAAA,KAAM,CAAI,CAAA,EAAA;AAC5B,UAAA,IAAK,KAA8B,iBAAsB,KAAA,IAAA;AACvD,YAAO,OAAA,IAAA,CAAA;AACT,UAAA,IAAA;AAAA,YACE,CAAA,6CAAA,EAAgD,GAAG,CAAA,2BAAA,EAA8B,IAAK,CAAA,IAAA;AAAA,cACpF,MAAA;AAAA,aACD,CAAA,EAAA,CAAA;AAAA,WACH,CAAA;AACA,UAAO,OAAA,KAAA,CAAA;AAAA,SACT;AACA,QAAMD,MAAAA,KAAAA,GAAQ,IAAY,GAAG,CAAA,CAAA;AAC7B,QAAA,MAAM,QAAQ,YAAaA,CAAAA,KAAAA,EAAM,KAAM,CAAA,GAAG,GAAG,IAAI,CAAA,CAAA;AACjD,QAAI,IAAA,OAAO,UAAU,QAAU,EAAA;AAC7B,UAAA,IAAA,CAAK,YAAY,GAAG,CAAA;AAAA,CAAkC,EAAA,MAAA,CAAO,KAAK,CAAC,CAAE,CAAA,CAAA,CAAA;AAAA,SACvE;AACA,QAAA,OAAO,KAAU,KAAA,IAAA,CAAA;AAAA,OAClB,CAAA,CAAA;AAAA,KACH;AAAA,GACD,CAAA,CAAA;AAED,EAAO,MAAA,CAAA,cAAA,CAAe,MAAM,mBAAqB,EAAA;AAAA,IAC/C,QAAU,EAAA,IAAA;AAAA,IACV,KAAO,EAAA,KAAA;AAAA,GACR,CAAA,CAAA;AAED,EAAO,MAAA,CAAA,cAAA,CAAe,MAAM,OAAS,EAAA;AAAA,IACnC,GAAM,GAAA;AACJ,MAAA,IAAA,CAAK,iBAAoB,GAAA,IAAA,CAAA;AACzB,MAAO,OAAA,IAAA,CAAA;AAAA,KACT;AAAA,GACD,CAAA,CAAA;AAED,EAAO,OAAA,IAAA,CAAA;AACT;;;;;;;;ACvCA,MAAM,+BAAiC,CAAA,MAAA;AAjCvC,EAAA,IAAA,EAAA,CAAA;AAmCE,EAAA;AAAA;AAAA,IAAmB,EAAA,GAAA,MAAA;AAAA,MAOjB,WAAW,GAAM,GAAA;AACf,QAAA,OAAO,GAAI,EAAA,CAAA;AAAA,OACb;AAAA,MACA,WAAW,IAAO,GAAA;AAChB,QAAA,OAAO,IAAK,EAAA,CAAE,GAAI,CAAA,IAAA,CAAK,SAAS,IAAI,CAAA,CAAA;AAAA,OACtC;AAAA,MACA,WAAW,IAAO,GAAA;AAEhB,QAAI,IAAA,IAAA,CAAK,QAAS,CAAA,IAAA,KAAS,KAAW,CAAA,EAAA;AACpC,UAAA,OAAO,IAAK,EAAA,CAAA;AAAA,SACd;AACA,QAAA,OAAO,IAAK,EAAA,CAAE,GAAI,CAAA,IAAA,CAAK,SAAS,IAAI,CAAA,CAAA;AAAA,OACtC;AAAA,MACA,WAAW,MAAS,GAAA;AAClB,QAAA,OAAO,MAAO,EAAA,CAAE,GAAI,CAAA,IAAA,CAAK,SAAS,MAAM,CAAA,CAAA;AAAA,OAC1C;AAAA,MACA,WAAW,MAAS,GAAA;AAClB,QAAA,OAAO,MAAO,EAAA,CAAE,GAAI,CAAA,IAAA,CAAK,SAAS,MAAM,CAAA,CAAA;AAAA,OAC1C;AAAA,MACA,WAAW,KAAQ,GAAA;AACjB,QAAA,OAAO,KAAM,EAAA,CAAE,GAAI,CAAA,IAAA,CAAK,SAAS,KAAK,CAAA,CAAA;AAAA,OACxC;AAAA,MACA,WAAW,MAAS,GAAA;AAClB,QAAA,OAAO,MAAO,EAAA,CAAE,GAAI,CAAA,IAAA,CAAK,SAAS,MAAM,CAAA,CAAA;AAAA,OAC1C;AAAA,MACA,WAAW,OAAU,GAAA;AACnB,QAAA,OAAO,OAAQ,EAAA,CAAE,GAAI,CAAA,IAAA,CAAK,SAAS,OAAO,CAAA,CAAA;AAAA,OAC5C;AAAA,MACA,WAAW,MAAS,GAAA;AAClB,QAAA,OAAO,MAAO,EAAA,CAAA;AAAA,OAChB;AAAA,MAEA,WAAW,QAAW,GAAA;AACpB,QAAA,OAAO,QAAS,EAAA,CAAA;AAAA,OAClB;AAAA;AAAA,MAWA,OAAO,UAAU,IAAa,EAAA;AAC5B,QAAA,IAAA;AAAA,UACE,CAAA,+LAAA,CAAA;AAAA,SACF,CAAA;AAAA,OACF;AAAA,OAvDA,aADF,CAAA,EAAA,EACS,YAAsC,EAAC,CAAA,EAE9C,cAHF,EAGS,EAAA,kBAAA,CAAA,EAEP,cALF,EAKS,EAAA,QAAA,EAAS,SAsChB,aA3CF,CAAA,EAAA,EA2CkB,UAAS,MACzB,CAAA,EAAA,aAAA,CA5CF,IA4CkB,OAAQ,EAAA,KAAA,CAAA,EACxB,cA7CF,EA6CkB,EAAA,YAAA,EAAa,aAC7B,aA9CF,CAAA,EAAA,EA8CkB,aAAY,SAC5B,CAAA,EAAA,aAAA,CA/CF,IA+CkB,SAAU,EAAA,OAAA,CAAA,EAC1B,cAhDF,EAgDkB,EAAA,UAAA,EAAW,WAC3B,aAjDF,CAAA,EAAA,EAiDkB,SAAQ,KASxB,CAAA,EAAA,aAAA,CA1DF,IA0DS,OAAQ,EAAA;AAAA,MACb,QAAA,CAAe,OAAU,IAAS,EAAA;AAChC,QAAA,OAAO,YAAmB,CAAA,IAAA,EAAM,KAAO,EAAA,IAAI,CAAM,KAAA,IAAA,CAAA;AAAA,OACnD;AAAA,MACA,MACE,CAAA,IAAA,EACA,GACA,EAAA,SAAA,GAAuB,KAC0C,EAAA;AACjE,QAAA,OACE,YAAY,eAAmB,CAAA,IAAA,EAAM,GAAG,CAAI,GAAA,MAAA,CAAU,MAAM,GAAG,CAAA,CAAA;AAAA,OAEnE;AAAA,KAtEJ,CAAA,EAAA,EAAA;AAAA,IAAA;AAAA,CAwEG,GAAA,CAAA;AAEL,SAAS,WAAA,CAAY,IAAkC,GAAA,YAAA,EAAgB,EAAA;AA7GvE,EAAA,IAAA,EAAA,CAAA;AA8GE,EAAA,OAAO,mBAAc,YAAa,CAAA;AAAA,IAGhC,WAAW,gBAAmB,GAAA;AAC5B,MAAO,OAAA,EAAE,GAAG,IAAA,CAAK,QAAS,EAAA,CAAA;AAAA,KAC5B;AAAA,IAEA,WAAW,iBAAiB,CAAwC,EAAA;AAClE,MAAA,IAAI,MAAM,KAAO,EAAA;AACf,QAAA,IAAA,CAAK,WAAW,EAAC,CAAA;AACjB,QAAA,OAAA;AAAA,OACF;AACA,MAAA,IAAI,MAAM,IAAM,EAAA;AACd,QAAK,IAAA,CAAA,QAAA,GAAW,EAAE,GAAG,IAAK,EAAA,CAAA;AAC1B,QAAA,OAAA;AAAA,OACF;AACA,MAAK,IAAA,CAAA,QAAA,GAAW,EAAE,GAAG,CAAE,EAAA,CAAA;AAAA,KACzB;AAAA,KAhBA,aADK,CAAA,EAAA,EACE,YAAsC,EAAE,GAAG,MAD7C,CAAA,EAAA,EAAA,CAAA;AAmBT,CAAA;AAEqB,MAAA,QAAA,SAA+B,aAAc,CAAA;AAAC;;;;"}