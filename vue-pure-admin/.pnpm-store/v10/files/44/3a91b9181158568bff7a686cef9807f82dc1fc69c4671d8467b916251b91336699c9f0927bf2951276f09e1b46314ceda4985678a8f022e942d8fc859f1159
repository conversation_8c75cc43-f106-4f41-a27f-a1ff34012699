declare const _default: __VLS_WithTemplateSlots<
  import('vue').DefineComponent<
    {
      x: {
        type: import('vue').PropType<number>
        required: true
      }
      y: {
        type: import('vue').PropType<number>
        required: true
      }
      label: {
        type: import('vue').PropType<
          | string
          | object
          | import('vue').VNode<
              import('vue').RendererNode,
              import('vue').RendererElement,
              {
                [key: string]: any
              }
            >
        >
      }
      labelStyle: {
        type: import('vue').PropType<import('vue').CSSProperties>
      }
      labelShowBg: {
        type: import('vue').PropType<boolean>
      }
      labelBgStyle: {
        type: import('vue').PropType<import('vue').CSSProperties>
      }
      labelBgPadding: {
        type: import('vue').PropType<[number, number]>
      }
      labelBgBorderRadius: {
        type: import('vue').PropType<number>
      }
    },
    {},
    unknown,
    {},
    {},
    import('vue').ComponentOptionsMixin,
    import('vue').ComponentOptionsMixin,
    {},
    string,
    import('vue').VNodeProps & import('vue').AllowedComponentProps & import('vue').ComponentCustomProps,
    Readonly<
      import('vue').ExtractPropTypes<{
        x: {
          type: import('vue').PropType<number>
          required: true
        }
        y: {
          type: import('vue').PropType<number>
          required: true
        }
        label: {
          type: import('vue').PropType<
            | string
            | object
            | import('vue').VNode<
                import('vue').RendererNode,
                import('vue').RendererElement,
                {
                  [key: string]: any
                }
              >
          >
        }
        labelStyle: {
          type: import('vue').PropType<import('vue').CSSProperties>
        }
        labelShowBg: {
          type: import('vue').PropType<boolean>
        }
        labelBgStyle: {
          type: import('vue').PropType<import('vue').CSSProperties>
        }
        labelBgPadding: {
          type: import('vue').PropType<[number, number]>
        }
        labelBgBorderRadius: {
          type: import('vue').PropType<number>
        }
      }>
    >,
    {},
    {}
  >,
  {
    default?(_: {}): any
  }
>
export default _default
type __VLS_WithTemplateSlots<T, S> = T & {
  new (): {
    $slots: S
  }
}
